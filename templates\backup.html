{% extends "base.html" %}

{% block title %}النسخ الاحتياطي والاستعادة - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-database"></i>
        النسخ الاحتياطي والاستعادة
    </h1>
    <p class="page-subtitle">إدارة النسخ الاحتياطية وإفراغ البيانات</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" onclick="createBackup()">
        <i class="fas fa-download"></i>
        إنشاء نسخة احتياطية
    </button>
    <button class="btn btn-success" onclick="refreshStats()">
        <i class="fas fa-sync-alt"></i>
        تحديث الإحصائيات
    </button>
    <button class="btn btn-info" onclick="exportData()">
        <i class="fas fa-file-export"></i>
        تصدير البيانات
    </button>
</div>

<!-- Backup and Restore Cards -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-download"></i>
                    إنشاء نسخة احتياطية
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">إنشاء نسخة احتياطية كاملة من قاعدة البيانات</p>

                <div class="mb-3">
                    <label for="backupName" class="form-label">اسم النسخة الاحتياطية</label>
                    <input type="text" class="form-control" id="backupName"
                           placeholder="اتركه فارغاً لاستخدام التاريخ والوقت الحالي">
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="includeSystemLog" checked>
                        <label class="form-check-label" for="includeSystemLog">
                            تضمين سجل النظام
                        </label>
                    </div>
                </div>

                <button class="btn btn-primary w-100" onclick="createBackup()">
                    <i class="fas fa-download"></i>
                    إنشاء نسخة احتياطية
                </button>

                <!-- Progress Bar -->
                <div id="backupProgress" class="mt-3" style="display: none;">
                    <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                    </div>
                    <small class="text-muted">جاري إنشاء النسخة الاحتياطية...</small>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0">
                    <i class="fas fa-upload"></i>
                    استعادة النسخة الاحتياطية
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text">استعادة البيانات من نسخة احتياطية سابقة</p>

                <div class="mb-3">
                    <label for="backupFile" class="form-label">ملف النسخة الاحتياطية</label>
                    <input type="file" class="form-control" id="backupFile" accept=".bak">
                    <div class="form-text">يدعم ملفات .bak فقط (نسخ احتياطية SQL Server)</div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="confirmRestore">
                        <label class="form-check-label" for="confirmRestore">
                            أؤكد أنني أريد استبدال البيانات الحالية
                        </label>
                    </div>
                </div>

                <button class="btn btn-success w-100" onclick="restoreBackup()" disabled id="restoreBtn">
                    <i class="fas fa-upload"></i>
                    استعادة النسخة الاحتياطية
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics and Management Section -->
<div class="row mb-4">
    <!-- Database Statistics -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar"></i>
                    إحصائيات قاعدة البيانات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <tbody id="dbStats">
                            <tr>
                                <td>الجامعات</td>
                                <td><span class="badge bg-primary" id="universitiesCount">-</span></td>
                            </tr>
                            <tr>
                                <td>الكليات</td>
                                <td><span class="badge bg-primary" id="collegesCount">-</span></td>
                            </tr>
                            <tr>
                                <td>الأقسام</td>
                                <td><span class="badge bg-primary" id="departmentsCount">-</span></td>
                            </tr>
                            <tr>
                                <td>التخصصات</td>
                                <td><span class="badge bg-primary" id="specializationsCount">-</span></td>
                            </tr>
                            <tr>
                                <td>البرامج الأكاديمية</td>
                                <td><span class="badge bg-primary" id="academicProgramsCount">-</span></td>
                            </tr>
                            <tr>
                                <td>الفصول الدراسية</td>
                                <td><span class="badge bg-secondary" id="semestersCount">-</span></td>
                            </tr>
                            <tr>
                                <td>الطلبة</td>
                                <td><span class="badge bg-success" id="studentsCount">-</span></td>
                            </tr>
                            <tr>
                                <td>أعضاء هيئة التدريس</td>
                                <td><span class="badge bg-success" id="facultyCount">-</span></td>
                            </tr>
                            <tr>
                                <td>المستخدمين</td>
                                <td><span class="badge bg-success" id="usersCount">-</span></td>
                            </tr>

                            <tr>
                                <td>سجل النظام</td>
                                <td><span class="badge bg-danger" id="systemLogCount">-</span></td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <button class="btn btn-info w-100" onclick="refreshStats()">
                    <i class="fas fa-sync-alt"></i>
                    تحديث الإحصائيات
                </button>
            </div>
        </div>
    </div>

    <!-- Data Management -->
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0">
                    <i class="fas fa-broom"></i>
                    إدارة البيانات
                </h5>
            </div>
            <div class="card-body">
                <p class="card-text mb-2">حذف جميع البيانات من الجداول المحددة</p>

                <div class="mb-2">
                    <label class="form-label mb-1">اختر الجداول المراد إفراغها:</label>

                    <!-- البيانات الأساسية -->
                    <div class="mb-1">
                        <small class="text-muted fw-bold">البيانات الأساسية:</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearUniversities">
                        <label class="form-check-label" for="clearUniversities">الجامعات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearColleges">
                        <label class="form-check-label" for="clearColleges">الكليات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearDepartments">
                        <label class="form-check-label" for="clearDepartments">الأقسام</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearSpecializations">
                        <label class="form-check-label" for="clearSpecializations">التخصصات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearAcademicPrograms">
                        <label class="form-check-label" for="clearAcademicPrograms">البرامج الأكاديمية</label>
                    </div>

                    <!-- البيانات الأكاديمية -->
                    <div class="mb-1 mt-2">
                        <small class="text-muted fw-bold">البيانات الأكاديمية:</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearAcademicYears">
                        <label class="form-check-label" for="clearAcademicYears">الأعوام الدراسية</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearSemesters">
                        <label class="form-check-label" for="clearSemesters">الفصول الدراسية</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearSubjects">
                        <label class="form-check-label" for="clearSubjects">المواد الدراسية</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearAdmissionChannels">
                        <label class="form-check-label" for="clearAdmissionChannels">قنوات القبول</label>
                    </div>

                    <!-- بيانات الأشخاص -->
                    <div class="mb-1 mt-2">
                        <small class="text-muted fw-bold">بيانات الأشخاص:</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearStudents">
                        <label class="form-check-label" for="clearStudents">بيانات الطلبة</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearFaculty">
                        <label class="form-check-label" for="clearFaculty">أعضاء هيئة التدريس</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearUsers">
                        <label class="form-check-label" for="clearUsers">المستخدمين</label>
                    </div>

                    <!-- البيانات الأكاديمية المتقدمة -->
                    <div class="mb-1 mt-2">
                        <small class="text-muted fw-bold">البيانات الأكاديمية المتقدمة:</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearGrades">
                        <label class="form-check-label" for="clearGrades">الدرجات</label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearResearchSubjects">
                        <label class="form-check-label" for="clearResearchSubjects">مواد السنة البحثية</label>
                    </div>

                    <!-- بيانات النظام -->
                    <div class="mb-1 mt-2">
                        <small class="text-muted fw-bold">بيانات النظام:</small>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="clearSystemLog">
                        <label class="form-check-label" for="clearSystemLog">سجل النظام</label>
                    </div>

                    <!-- خيار حذف جميع البيانات -->
                    <div class="mb-2 mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="clearAll">
                            <label class="form-check-label" for="clearAll">
                                <strong class="text-danger">جميع البيانات (خطر!)</strong>
                            </label>
                        </div>
                    </div>
                </div>

                <button class="btn btn-warning w-100" onclick="clearData()">
                    <i class="fas fa-broom"></i>
                    إفراغ البيانات المحددة
                </button>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    refreshStats();
    
    // تفعيل زر الاستعادة عند تأكيد الاستعادة
    document.getElementById('confirmRestore').addEventListener('change', function() {
        const restoreBtn = document.getElementById('restoreBtn');
        const fileInput = document.getElementById('backupFile');
        
        restoreBtn.disabled = !(this.checked && fileInput.files.length > 0);
    });
    
    // تفعيل زر الاستعادة عند اختيار ملف
    document.getElementById('backupFile').addEventListener('change', function() {
        const restoreBtn = document.getElementById('restoreBtn');
        const confirmRestore = document.getElementById('confirmRestore');
        
        restoreBtn.disabled = !(this.files.length > 0 && confirmRestore.checked);
    });
    
    // تحديد/إلغاء تحديد جميع الجداول
    document.getElementById('clearAll').addEventListener('change', function() {
        const checkboxes = [
            'clearUniversities', 'clearColleges', 'clearDepartments', 'clearSpecializations',
            'clearAcademicPrograms', 'clearAcademicYears', 'clearSemesters', 'clearSubjects',
            'clearAdmissionChannels', 'clearStudents', 'clearFaculty', 'clearUsers',
            'clearGrades', 'clearResearchSubjects', 'clearSystemLog'
        ];
        checkboxes.forEach(id => {
            const checkbox = document.getElementById(id);
            if (checkbox) {
                checkbox.checked = this.checked;
            }
        });
    });
});

async function createBackup() {
    const backupName = document.getElementById('backupName').value.trim();
    const includeSystemLog = document.getElementById('includeSystemLog').checked;

    // إظهار شريط التقدم
    const progressDiv = document.getElementById('backupProgress');
    if (!progressDiv) {
        console.error('عنصر backupProgress غير موجود');
        showErrorModal('خطأ في واجهة المستخدم', 'خطأ في النظام!');
        return;
    }

    const progressBar = progressDiv.querySelector('.progress-bar');
    progressDiv.style.display = 'block';
    progressBar.style.width = '0%';

    try {
        showInfoModal('جاري إنشاء النسخة الاحتياطية...', 'إنشاء النسخة الاحتياطية');

        // محاكاة التقدم
        let progress = 0;
        const interval = setInterval(() => {
            progress += 10;
            progressBar.style.width = progress + '%';

            if (progress >= 90) {
                clearInterval(interval);
            }
        }, 200);

        // إرسال طلب النسخ الاحتياطي
        const response = await fetch('/api/backup/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                backup_name: backupName,
                include_system_log: includeSystemLog
            })
        });

        clearInterval(interval);
        progressBar.style.width = '100%';

        if (response.ok) {
            const blob = await response.blob();

            // تحميل الملف
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            const fileName = (backupName || 'backup') + '_' + new Date().toISOString().split('T')[0] + '.bak';
            a.download = fileName;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            showSuccessModal('تم إنشاء النسخة الاحتياطية وتحميلها بنجاح', 'نسخ احتياطي ناجح!');

            // إعادة تعيين النموذج
            document.getElementById('backupName').value = '';

            // تحديث الإحصائيات
            refreshStats();
        } else {
            let errorMessage = 'فشل في إنشاء النسخة الاحتياطية';
            try {
                const error = await response.json();
                errorMessage = error.error || errorMessage;
            } catch (e) {
                errorMessage = `خطأ HTTP ${response.status}: ${response.statusText}`;
            }
            throw new Error(errorMessage);
        }
    } catch (error) {
        console.error('خطأ في النسخ الاحتياطي:', error);
        showErrorModal('حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' + error.message, 'فشل النسخ الاحتياطي!');
    } finally {
        progressDiv.style.display = 'none';
    }
}

async function restoreBackup() {
    const fileInput = document.getElementById('backupFile');

    if (!fileInput.files.length) {
        showWarningModal('يرجى اختيار ملف النسخة الاحتياطية', 'ملف مطلوب!');
        return;
    }

    if (!confirm('تحذير: سيتم استبدال جميع البيانات الحالية!\nهل أنت متأكد من المتابعة؟')) {
        return;
    }

    try {
        showInfoModal('جاري استعادة النسخة الاحتياطية...', 'استعادة النسخة الاحتياطية');

        const formData = new FormData();
        formData.append('backup_file', fileInput.files[0]);

        const response = await fetch('/api/backup/restore', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            showSuccessModal('تم استعادة النسخة الاحتياطية بنجاح', 'استعادة ناجحة!');
            refreshStats();
            // إعادة تعيين النموذج
            fileInput.value = '';
            document.getElementById('confirmRestore').checked = false;
            document.getElementById('restoreBtn').disabled = true;
        } else {
            showErrorModal(result.error || 'حدث خطأ أثناء استعادة النسخة الاحتياطية', 'فشل الاستعادة!');
        }

    } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        showErrorModal('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + error.message, 'فشل الاستعادة!');
    }
}

async function clearData() {
    const selectedTables = [];
    const tableMapping = {
        'clearUniversities': { name: 'الجامعات', type: 'universities' },
        'clearColleges': { name: 'الكليات', type: 'colleges' },
        'clearDepartments': { name: 'الأقسام', type: 'departments' },
        'clearSpecializations': { name: 'التخصصات', type: 'specializations' },
        'clearAcademicPrograms': { name: 'البرامج الأكاديمية', type: 'academic_programs' },
        'clearAcademicYears': { name: 'الأعوام الدراسية', type: 'academic_years' },
        'clearSemesters': { name: 'الفصول الدراسية', type: 'semesters' },
        'clearSubjects': { name: 'المواد الدراسية', type: 'subjects' },
        'clearAdmissionChannels': { name: 'قنوات القبول', type: 'admission_channels' },
        'clearStudents': { name: 'بيانات الطلبة', type: 'students' },
        'clearFaculty': { name: 'أعضاء هيئة التدريس', type: 'faculty' },
        'clearUsers': { name: 'المستخدمين', type: 'users' },
        'clearGrades': { name: 'الدرجات', type: 'grades' },
        'clearResearchSubjects': { name: 'مواد السنة البحثية', type: 'research_subjects' },
        'clearSystemLog': { name: 'سجل النظام', type: 'system_log' }
    };

    let clearType = '';

    if (document.getElementById('clearAll').checked) {
        selectedTables.push('جميع البيانات');
        clearType = 'all';
    } else {
        // فحص كل جدول محدد
        for (const [checkboxId, tableInfo] of Object.entries(tableMapping)) {
            const checkbox = document.getElementById(checkboxId);
            if (checkbox && checkbox.checked) {
                selectedTables.push(tableInfo.name);
                if (!clearType) {
                    clearType = tableInfo.type;
                } else {
                    clearType = 'multiple'; // إذا تم اختيار أكثر من جدول
                }
            }
        }
    }

    if (selectedTables.length === 0) {
        showWarningModal('يرجى اختيار الجداول المراد إفراغها', 'اختيار مطلوب!');
        return;
    }

    const tablesText = selectedTables.join('، ');

    if (!confirm(`تحذير: سيتم حذف البيانات من: ${tablesText}\nهذه العملية لا يمكن التراجع عنها!\nهل أنت متأكد؟`)) {
        return;
    }

    try {
        showInfoModal('جاري إفراغ البيانات...', 'إفراغ البيانات');

        // إعداد البيانات للإرسال
        let requestData = { clear_type: clearType };

        // إذا كان النوع multiple، أرسل قائمة الجداول المحددة
        if (clearType === 'multiple') {
            const selectedTableTypes = [];
            for (const [checkboxId, tableInfo] of Object.entries(tableMapping)) {
                const checkbox = document.getElementById(checkboxId);
                if (checkbox && checkbox.checked) {
                    selectedTableTypes.push(tableInfo.type);
                }
            }
            requestData.tables = selectedTableTypes;
        }

        const response = await fetch('/api/backup/clear_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });

        const result = await response.json();

        if (response.ok) {
            showDeleteSuccess('تم إفراغ البيانات المحددة بنجاح');
            refreshStats();

            // إعادة تعيين الخيارات
            document.querySelectorAll('input[type="checkbox"]').forEach(cb => {
                if (cb.id.startsWith('clear')) cb.checked = false;
            });
        } else {
            showDeleteError(result.error || 'حدث خطأ أثناء إفراغ البيانات');
        }

    } catch (error) {
        console.error('خطأ في إفراغ البيانات:', error);
        showDeleteError('حدث خطأ أثناء إفراغ البيانات: ' + error.message);
    }
}

async function refreshStats() {
    try {
        showInfoModal('جاري تحديث الإحصائيات...', 'تحديث الإحصائيات');

        const response = await fetch('/api/stats');
        if (response.ok) {
            const stats = await response.json();

            // البيانات الأساسية
            document.getElementById('universitiesCount').textContent = stats.universities || 0;
            document.getElementById('collegesCount').textContent = stats.colleges || 0;
            document.getElementById('departmentsCount').textContent = stats.departments || 0;
            document.getElementById('specializationsCount').textContent = stats.specializations || 0;
            document.getElementById('academicProgramsCount').textContent = stats.academic_programs || 0;

            // البيانات الأكاديمية
            document.getElementById('semestersCount').textContent = stats.semesters || 0;

            // بيانات الأشخاص
            document.getElementById('studentsCount').textContent = stats.students || 0;
            document.getElementById('facultyCount').textContent = stats.faculty || 0;
            document.getElementById('usersCount').textContent = stats.users || 0;

            // بيانات النظام
            document.getElementById('systemLogCount').textContent = stats.system_log || 0;

            showUpdateSuccess('تم تحديث الإحصائيات');
        } else {
            throw new Error('فشل في تحميل الإحصائيات');
        }

    } catch (error) {
        console.error('خطأ في تحديث الإحصائيات:', error);
        showUpdateError('حدث خطأ أثناء تحديث الإحصائيات');
    }
}





// مسح بيانات الطلبة
function clearStudentData() {
    showConfirmModal('مسح بيانات الطلبة', 'سيتم حذف جميع بيانات الطلبة والدرجات نهائياً. هل أنت متأكد؟', () => {
        clearDataType('students');
    });
}

// مسح بيانات هيئة التدريس
function clearFacultyData() {
    showConfirmModal('مسح بيانات هيئة التدريس', 'سيتم حذف جميع بيانات أعضاء هيئة التدريس نهائياً. هل أنت متأكد؟', () => {
        clearDataType('faculty');
    });
}

// مسح جميع البيانات
function clearAllData() {
    showConfirmModal('مسح جميع البيانات', 'سيتم حذف جميع البيانات من النظام نهائياً. هل أنت متأكد؟', () => {
        clearDataType('all');
    });
}

// مسح نوع معين من البيانات
async function clearDataType(clearType) {
    try {
        const response = await fetch('/api/backup/clear_data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                clear_type: clearType
            })
        });

        if (response.ok) {
            const result = await response.json();
            showDeleteSuccess(result.message);
        } else {
            const error = await response.json();
            throw new Error(error.error || 'فشل في مسح البيانات');
        }
    } catch (error) {
        console.error('خطأ في مسح البيانات:', error);
        showDeleteError('حدث خطأ أثناء مسح البيانات: ' + error.message);
    }
}

// عرض نافذة التأكيد
function showConfirmModal(title, message, callback) {
    document.getElementById('confirmMessage').textContent = message;
    document.getElementById('confirmBtn').onclick = () => {
        bootstrap.Modal.getInstance(document.getElementById('confirmModal')).hide();
        callback();
    };

    const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
    modal.show();
}

// تأكيد استعادة النسخة الاحتياطية
async function confirmRestore() {
    const fileInput = document.getElementById('backupFile');

    try {
        showInfoModal('جاري استعادة النسخة الاحتياطية...', 'استعادة النسخة الاحتياطية');

        const formData = new FormData();
        formData.append('backup_file', fileInput.files[0]);

        const response = await fetch('/api/backup/restore', {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (response.ok) {
            showSuccessModal('تم استعادة النسخة الاحتياطية بنجاح', 'استعادة ناجحة!');
            refreshStats();
            // إعادة تعيين النموذج
            fileInput.value = '';
            document.getElementById('confirmRestore').checked = false;
            document.getElementById('restoreBtn').disabled = true;
        } else {
            showErrorModal(result.error || 'حدث خطأ أثناء استعادة النسخة الاحتياطية', 'فشل الاستعادة!');
        }

    } catch (error) {
        console.error('خطأ في استعادة النسخة الاحتياطية:', error);
        showErrorModal('حدث خطأ أثناء استعادة النسخة الاحتياطية: ' + error.message, 'فشل الاستعادة!');
    }
}

// تصدير البيانات
function exportData() {
    showInfoModal('سيتم تطوير وظيفة التصدير قريباً', 'قريباً!');
}
</script>
{% endblock %}
