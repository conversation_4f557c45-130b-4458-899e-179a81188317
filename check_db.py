from database_config import get_db_connection

def check_database_structure():
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        print('=== جداول قاعدة البيانات ===')
        cursor.execute("SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' ORDER BY TABLE_NAME")
        tables = cursor.fetchall()
        for table in tables:
            print(f'جدول: {table[0]}')

        print('\n=== أعمدة جدول academic_years ===')
        try:
            cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'academic_years' ORDER BY ORDINAL_POSITION")
            columns = cursor.fetchall()
            for col in columns:
                print(f'عمود: {col[0]} - نوع: {col[1]}')
        except Exception as e:
            print(f'خطأ في جدول academic_years: {e}')

        print('\n=== أعمدة جدول academic_programs ===')
        try:
            cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'academic_programs' ORDER BY ORDINAL_POSITION")
            columns = cursor.fetchall()
            for col in columns:
                print(f'عمود: {col[0]} - نوع: {col[1]}')
        except Exception as e:
            print(f'خطأ في جدول academic_programs: {e}')

        print('\n=== أعمدة جدول semesters ===')
        try:
            cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'semesters' ORDER BY ORDINAL_POSITION")
            columns = cursor.fetchall()
            for col in columns:
                print(f'عمود: {col[0]} - نوع: {col[1]}')
        except Exception as e:
            print(f'خطأ في جدول semesters: {e}')

        print('\n=== أعمدة جدول students ===')
        try:
            cursor.execute("SELECT COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'students' ORDER BY ORDINAL_POSITION")
            columns = cursor.fetchall()
            for col in columns:
                print(f'عمود: {col[0]} - نوع: {col[1]}')
        except Exception as e:
            print(f'خطأ في جدول students: {e}')

        conn.close()

    except Exception as e:
        print(f'خطأ عام: {e}')

if __name__ == '__main__':
    check_database_structure()
