{% extends "base.html" %}

{% block title %}إدارة البرامج الأكاديمية - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-graduation-cap"></i>
        إدارة البرامج الأكاديمية
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات البرامج الأكاديمية</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProgramModal">
        <i class="fas fa-plus"></i>
        إضافة برنامج أكاديمي جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="input-group">
                    <span class="input-group-text">
                        <i class="fas fa-search"></i>
                    </span>
                    <input type="text" class="form-control" id="searchInput" placeholder="البحث في البرامج الأكاديمية...">
                    <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-6">
                <select class="form-select" id="departmentFilter" onchange="filterPrograms()">
                    <option value="">جميع الأقسام</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- Programs Table -->
<div class="card">
    <div class="card-header">
        <h5 class="card-title mb-0">
            <i class="fas fa-list"></i>
            قائمة البرامج الأكاديمية
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th width="5%">#</th>
                        <th width="25%">اسم البرنامج</th>
                        <th width="20%">القسم</th>
                        <th width="15%">نوع البرنامج</th>
                        <th width="10%">المدة</th>
                        <th width="15%">تاريخ الإنشاء</th>
                        <th width="10%">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="programsTableBody">
                    <!-- سيتم ملء البيانات هنا بواسطة JavaScript -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Program Modal -->
<div class="modal fade" id="addProgramModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة برنامج أكاديمي جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="programForm">
                    <input type="hidden" id="programId" name="program_id">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">اسم البرنامج *</label>
                        <input type="text" class="form-control" id="name" name="name" required maxlength="255" 
                               placeholder="أدخل اسم البرنامج الأكاديمي">
                        <div class="form-text">أدخل اسم البرنامج الأكاديمي</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="departmentId" class="form-label">القسم *</label>
                        <select class="form-select" id="departmentId" name="department_id" required>
                            <option value="">اختر القسم</option>
                        </select>
                        <div class="form-text">اختر القسم التابع له البرنامج</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="programType" class="form-label">نوع البرنامج *</label>
                            <select class="form-select" id="programType" name="program_type" required>
                                <option value="">اختر نوع البرنامج</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="دبلوم عالي">دبلوم عالي</option>
                            </select>
                            <div class="form-text">اختر نوع البرنامج الأكاديمي</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">مدة البرنامج (بالسنوات) *</label>
                            <input type="number" class="form-control" id="duration" name="duration" required 
                                   min="1" max="10" placeholder="مثال: 2">
                            <div class="form-text">أدخل مدة البرنامج بالسنوات</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveProgram()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let programs = [];
let departments = [];

// دالة عرض الرسائل
function showAlert(message, type) {
    // إنشاء عنصر التنبيه
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إضافة التنبيه إلى الصفحة
    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadPrograms();
    loadDepartments();
});

// تحميل قائمة الأقسام
function loadDepartments() {
    fetch('/api/departments')
        .then(response => response.json())
        .then(data => {
            departments = data;
            const departmentSelect = document.getElementById('departmentId');
            const departmentFilter = document.getElementById('departmentFilter');
            
            departmentSelect.innerHTML = '<option value="">اختر القسم</option>';
            departmentFilter.innerHTML = '<option value="">جميع الأقسام</option>';
            
            data.forEach(department => {
                const option = document.createElement('option');
                option.value = department.department_id;
                option.textContent = department.name;
                departmentSelect.appendChild(option);
                
                const filterOption = document.createElement('option');
                filterOption.value = department.department_id;
                filterOption.textContent = department.name;
                departmentFilter.appendChild(filterOption);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الأقسام:', error);
        });
}

// تحميل قائمة البرامج الأكاديمية
function loadPrograms() {
    fetch('/api/academic_programs')
        .then(response => response.json())
        .then(data => {
            programs = data;
            displayPrograms(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showAlert('خطأ في تحميل البيانات', 'error');
        });
}

// عرض البرامج الأكاديمية في الجدول
function displayPrograms(data) {
    const tbody = document.getElementById('programsTableBody');
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="7" class="text-center py-4">
                    <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد برامج أكاديمية مسجلة حالياً</p>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((program, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${program.name}</td>
            <td>${program.department_name || '-'}</td>
            <td><span class="badge bg-primary">${program.program_type}</span></td>
            <td>${program.duration} سنة</td>
            <td>${program.created_date ? new Date(program.created_date).toLocaleDateString('en-GB') : '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editProgram(${program.program_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteProgram(${program.program_id}, '${program.name}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// إضافة أو تحديث برنامج أكاديمي
function saveProgram() {
    const form = document.getElementById('programForm');
    const programId = document.getElementById('programId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();
    const departmentId = form.querySelector('#departmentId').value;
    const programType = form.querySelector('#programType').value;
    const duration = form.querySelector('#duration').value;

    if (!name || !departmentId || !programType || !duration) {
        showAlert('جميع الحقول المطلوبة يجب ملؤها', 'error');
        return;
    }

    const data = {
        name: name,
        department_id: parseInt(departmentId),
        program_type: programType,
        duration: parseInt(duration)
    };

    const url = programId ? `/api/academic_programs/${programId}` : '/api/academic_programs';
    const method = programId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addProgramModal')).hide();
            form.reset();
            document.getElementById('programId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة برنامج أكاديمي جديد';
            loadPrograms();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ البرنامج الأكاديمي', 'error');
    });
}

// تعديل برنامج أكاديمي
function editProgram(programId) {
    const program = programs.find(p => p.program_id === programId);
    if (!program) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('programId').value = program.program_id;
    document.getElementById('name').value = program.name;
    document.getElementById('departmentId').value = program.department_id || '';
    document.getElementById('programType').value = program.program_type;
    document.getElementById('duration').value = program.duration;
    document.getElementById('modalTitle').textContent = 'تعديل بيانات البرنامج الأكاديمي';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addProgramModal')).show();
}

// حذف برنامج أكاديمي
function deleteProgram(programId, programName) {
    if (!confirm(`هل أنت متأكد من حذف البرنامج الأكاديمي "${programName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/academic_programs/${programId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadPrograms();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف البرنامج الأكاديمي', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterPrograms();
});

function filterPrograms() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const departmentFilter = document.getElementById('departmentFilter').value;

    const filteredData = programs.filter(program => {
        const matchesSearch = program.name.toLowerCase().includes(searchTerm) ||
                            (program.department_name && program.department_name.toLowerCase().includes(searchTerm)) ||
                            program.program_type.toLowerCase().includes(searchTerm);

        const matchesDepartment = !departmentFilter || program.department_id == departmentFilter;

        return matchesSearch && matchesDepartment;
    });

    displayPrograms(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('departmentFilter').value = '';
    displayPrograms(programs);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addProgramModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('programForm').reset();
            document.getElementById('programId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة برنامج أكاديمي جديد';
        });
    }
});
</script>
{% endblock %}
