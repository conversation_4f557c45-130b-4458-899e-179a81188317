{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<style>
    .border-left-primary {
        border-left: 4px solid var(--accent-color) !important;
    }
    
    .border-left-success {
        border-left: 4px solid var(--success-color) !important;
    }
    
    .border-left-info {
        border-left: 4px solid var(--info-color) !important;
    }
    
    .border-left-warning {
        border-left: 4px solid var(--warning-color) !important;
    }
    
    .text-primary {
        color: var(--accent-color) !important;
    }
    
    .text-success {
        color: var(--success-color) !important;
    }
    
    .text-info {
        color: var(--info-color) !important;
    }
    
    .text-warning {
        color: var(--warning-color) !important;
    }
    
    .text-gray-300 {
        color: var(--gray-300) !important;
    }
    
    .text-gray-800 {
        color: var(--gray-800) !important;
    }
    
    .text-xs {
        font-size: 0.9rem;
        font-weight: 500;
    }

    .font-weight-bold {
        font-weight: 700;
    }

    .text-uppercase {
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .h5 {
        font-size: 1.75rem;
        font-weight: 700;
    }
    
    .py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
    
    .no-gutters {
        margin-right: 0;
        margin-left: 0;
    }
    
    .no-gutters > .col,
    .no-gutters > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }
    
    .mr-2 {
        margin-right: 0.5rem;
    }
    
    .mb-1 {
        margin-bottom: 0.25rem;
    }
    
    .mb-0 {
        margin-bottom: 0;
    }
    
    .mb-4 {
        margin-bottom: 1.5rem;
    }
    
    .h-100 {
        height: 100%;
    }
    
    .shadow {
        box-shadow: var(--shadow) !important;
    }

    /* تحسينات البطاقات الإحصائية */
    .card.border-left-primary,
    .card.border-left-success,
    .card.border-left-info,
    .card.border-left-warning {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .card.border-left-primary::before,
    .card.border-left-success::before,
    .card.border-left-info::before,
    .card.border-left-warning::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.6s;
    }

    .card.border-left-primary:hover::before,
    .card.border-left-success:hover::before,
    .card.border-left-info:hover::before,
    .card.border-left-warning:hover::before {
        left: 100%;
    }

    .card.border-left-primary:hover,
    .card.border-left-success:hover,
    .card.border-left-info:hover,
    .card.border-left-warning:hover {
        transform: translateY(-8px) scale(1.05);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15) !important;
    }

    .card-body {
        position: relative;
        z-index: 1;
    }

    .fa-2x {
        font-size: 2.5rem !important;
    }

    /* تحسين الأزرار السريعة */
    .btn.py-3 {
        padding: 1.5rem !important;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }

    .btn.py-3:hover {
        transform: translateY(-5px) scale(1.08);
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.2);
    }

    .btn.py-3 i {
        transition: transform 0.3s ease;
    }

    .btn.py-3:hover i {
        transform: scale(1.2) rotateY(360deg);
    }

    /* Dashboard Digital Clock Styles */
    .dashboard-clock {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        padding: 1.2rem 1.5rem;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        position: relative;
        overflow: hidden;
        min-width: 200px;
        text-align: center;
    }

    .dashboard-clock::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        animation: shine 3s infinite;
    }

    @keyframes shine {
        0% { left: -100%; }
        100% { left: 100%; }
    }

    .dashboard-time {
        font-family: 'Courier New', monospace;
        font-size: 1.4rem;
        font-weight: bold;
        color: #ffffff;
        margin-bottom: 0.5rem;
        text-shadow: 0 0 10px rgba(255,255,255,0.5);
        letter-spacing: 2px;
        position: relative;
        z-index: 2;
    }

    .dashboard-date {
        font-size: 0.9rem;
        color: rgba(255,255,255,0.95);
        font-weight: 500;
        position: relative;
        z-index: 2;
    }

    .time-separator {
        animation: blink 1s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
</style>

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                <i class="fas fa-tachometer-alt"></i>
                لوحة التحكم
            </h1>
            <p class="page-subtitle">مرحباً بك في نظام إدارة الدراسات العليا</p>
        </div>

        <!-- Digital Clock -->
        <div class="dashboard-clock">
            <div class="dashboard-time" id="dashboardTime">
                00<span class="time-separator">:</span>00<span class="time-separator">:</span>00
            </div>
            <div class="dashboard-date" id="dashboardDate">
                الأحد، 1 يناير 2024
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;">
            <div class="card-body text-white">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.9;">
                            إجمالي الطلبة
                        </div>
                        <div class="h4 mb-0 font-weight-bold">{{ stats.total_students }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(255,255,255,0.2); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-user-graduate fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); border: none;">
            <div class="card-body text-white">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.9;">
                            أعضاء هيئة التدريس
                        </div>
                        <div class="h4 mb-0 font-weight-bold">{{ stats.total_faculty }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(255,255,255,0.2); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-chalkboard-teacher fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); border: none;">
            <div class="card-body text-white">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.9;">
                            البرامج الأكاديمية
                        </div>
                        <div class="h4 mb-0 font-weight-bold">{{ stats.total_programs }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(255,255,255,0.2); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-graduation-cap fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #fa709a 0%, #fee140 100%); border: none;">
            <div class="card-body text-white">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.9;">
                            المواد الدراسية
                        </div>
                        <div class="h4 mb-0 font-weight-bold">{{ stats.total_courses }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(255,255,255,0.2); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-book fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); border: none;">
            <div class="card-body text-dark">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.8; color: #2c3e50;">
                            الجامعات
                        </div>
                        <div class="h4 mb-0 font-weight-bold" style="color: #2c3e50;">{{ stats.total_universities }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(44,62,80,0.1); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-university fa-2x" style="color: #2c3e50;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%); border: none;">
            <div class="card-body text-dark">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.8; color: #2c3e50;">
                            الكليات
                        </div>
                        <div class="h4 mb-0 font-weight-bold" style="color: #2c3e50;">{{ stats.total_colleges }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(44,62,80,0.1); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-building fa-2x" style="color: #2c3e50;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%); border: none;">
            <div class="card-body text-white">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.9;">
                            الأقسام
                        </div>
                        <div class="h4 mb-0 font-weight-bold">{{ stats.total_departments }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(255,255,255,0.2); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-sitemap fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card shadow h-100 py-2 stat-card" style="background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%); border: none;">
            <div class="card-body text-dark">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1" style="opacity: 0.8; color: #2c3e50;">
                            التخصصات
                        </div>
                        <div class="h4 mb-0 font-weight-bold" style="color: #2c3e50;">{{ stats.total_specializations }}</div>
                    </div>
                    <div class="col-auto">
                        <div class="icon-circle" style="background: rgba(44,62,80,0.1); border-radius: 50%; padding: 15px;">
                            <i class="fas fa-tags fa-2x" style="color: #2c3e50;"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">الإجراءات السريعة</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3 mb-3">
                <a href="/students" class="btn btn-primary w-100 py-3">
                    <i class="fas fa-user-plus fa-2x d-block mb-2"></i>
                    إضافة طالب جديد
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/faculty" class="btn btn-success w-100 py-3">
                    <i class="fas fa-chalkboard-teacher fa-2x d-block mb-2"></i>
                    إضافة عضو هيئة تدريس
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/grades_entry" class="btn btn-info w-100 py-3">
                    <i class="fas fa-edit fa-2x d-block mb-2"></i>
                    إدخال الدرجات
                </a>
            </div>
            <div class="col-md-3 mb-3">
                <a href="/reports" class="btn btn-warning w-100 py-3">
                    <i class="fas fa-file-alt fa-2x d-block mb-2"></i>
                    عرض التقارير
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">الأنشطة الحديثة</h5>
            </div>
            <div class="card-body">
                {% if recent_activities %}
                    {% for activity in recent_activities %}
                    <div class="d-flex align-items-center mb-3 pb-3 {% if not loop.last %}border-bottom{% endif %}">
                        <div class="{% if activity.operation_type == 'INSERT' %}bg-success{% elif activity.operation_type == 'UPDATE' %}bg-info{% elif activity.operation_type == 'DELETE' %}bg-danger{% else %}bg-warning{% endif %} rounded-circle p-2 me-3">
                            <i class="fas {% if activity.operation_type == 'INSERT' %}fa-plus{% elif activity.operation_type == 'UPDATE' %}fa-edit{% elif activity.operation_type == 'DELETE' %}fa-trash{% else %}fa-cog{% endif %} text-white"></i>
                        </div>
                        <div>
                            <h6 class="mb-1">{{ activity.operation_name }}</h6>
                            <p class="mb-0 text-muted">{{ activity.target_object }}</p>
                            <small class="text-muted">
                                <i class="fas fa-user me-1"></i>{{ activity.username }} - {{ activity.time_ago }}
                            </small>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-history fa-3x text-muted mb-3"></i>
                        <p class="text-muted">لا توجد أنشطة حديثة</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h6 class="m-0">إحصائيات سريعة</h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="fas fa-user-check me-2 text-success"></i>الطلبة النشطون</span>
                    <span class="badge bg-success">{{ stats.active_students }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="fas fa-graduation-cap me-2 text-primary"></i>المتخرجون</span>
                    <span class="badge bg-primary">{{ stats.graduated_students }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <span><i class="fas fa-user-plus me-2 text-info"></i>الطلبة الجدد</span>
                    <span class="badge bg-info">{{ stats.new_students }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span><i class="fas fa-chart-line me-2 text-warning"></i>معدل النجاح</span>
                    <span class="badge bg-warning">{{ stats.success_rate }}%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.stat-card {
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.2) !important;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.stat-card:hover::before {
    left: 100%;
}

.icon-circle {
    transition: all 0.3s ease;
}

.stat-card:hover .icon-circle {
    transform: rotate(10deg) scale(1.1);
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes ripple {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

.stat-card:active {
    animation: pulse 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات تفاعلية للبطاقات الإحصائية الجديدة
    const statCards = document.querySelectorAll('.stat-card');

    statCards.forEach((card, index) => {
        // تأثير الظهور المتدرج
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';

        setTimeout(() => {
            card.style.transition = 'all 0.6s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 150);

        // تأثير النقر محسن
        card.addEventListener('click', function() {
            // إضافة تأثير النبضة
            this.style.animation = 'pulse 0.3s ease';

            // إزالة التأثير بعد انتهائه
            setTimeout(() => {
                this.style.animation = '';
            }, 300);
        });

        // تأثير تموج عند النقر
        card.addEventListener('click', function(e) {
            const ripple = document.createElement('span');
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = e.clientX - rect.left - size / 2;
            const y = e.clientY - rect.top - size / 2;

            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = x + 'px';
            ripple.style.top = y + 'px';
            ripple.style.position = 'absolute';
            ripple.style.borderRadius = '50%';
            ripple.style.background = 'rgba(255,255,255,0.3)';
            ripple.style.transform = 'scale(0)';
            ripple.style.animation = 'ripple 0.6s linear';
            ripple.style.pointerEvents = 'none';

            this.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        });
    });

    // تأثير العد التصاعدي للأرقام
    const numbers = document.querySelectorAll('.h5.mb-0');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const number = entry.target;
                const finalNumber = parseInt(number.textContent) || 0;
                let currentNumber = 0;
                const increment = finalNumber / 50;
                const duration = 1500;
                const stepTime = duration / 50;

                const timer = setInterval(() => {
                    currentNumber += increment;
                    if (currentNumber >= finalNumber) {
                        number.textContent = finalNumber;
                        clearInterval(timer);

                        // تأثير الوميض عند الانتهاء
                        number.style.textShadow = '0 0 15px rgba(52, 152, 219, 0.8)';
                        setTimeout(() => {
                            number.style.textShadow = 'none';
                        }, 500);
                    } else {
                        number.textContent = Math.floor(currentNumber);
                    }
                }, stepTime);

                observer.unobserve(number);
            }
        });
    }, observerOptions);

    numbers.forEach(number => {
        observer.observe(number);
    });

    // تأثيرات الأزرار السريعة
    const quickActionBtns = document.querySelectorAll('.btn.py-3');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px) scale(1.08)';
        });

        btn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });

        btn.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-2px) scale(1.05)';
        });

        btn.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-5px) scale(1.08)';
        });
    });



    // Dashboard Digital Clock Function
    function updateDashboardClock() {
        const now = new Date();

        // تحديث الوقت
        const hours = String(now.getHours()).padStart(2, '0');
        const minutes = String(now.getMinutes()).padStart(2, '0');
        const seconds = String(now.getSeconds()).padStart(2, '0');

        const timeElement = document.getElementById('dashboardTime');
        if (timeElement) {
            timeElement.innerHTML = `${hours}<span class="time-separator">:</span>${minutes}<span class="time-separator">:</span>${seconds}`;
        }

        // تحديث التاريخ
        const days = ['الأحد', 'الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت'];
        const months = [
            'يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو',
            'يوليو', 'أغسطس', 'سبتمبر', 'أكتوبر', 'نوفمبر', 'ديسمبر'
        ];

        const dayName = days[now.getDay()];
        const day = now.getDate();
        const month = months[now.getMonth()];
        const year = now.getFullYear();

        const dateElement = document.getElementById('dashboardDate');
        if (dateElement) {
            dateElement.textContent = `${dayName}، ${day} ${month} ${year}`;
        }
    }

    // تحديث الساعة كل ثانية
    setInterval(updateDashboardClock, 1000);

    // تحديث فوري عند تحميل الصفحة
    updateDashboardClock();
});
</script>

<style>
.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}
</style>
{% endblock %}
