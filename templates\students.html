{% extends "base.html" %}

{% block title %}إدارة الطلبة - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    .student-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    
    .student-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .student-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .student-body {
        padding: 20px;
    }
    
    .gender-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .gender-male { background-color: #007bff; color: white; }
    .gender-female { background-color: #e83e8c; color: white; }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-prep { background-color: #ffc107; color: #212529; }
    .status-research { background-color: #17a2b8; color: white; }
    .status-graduate { background-color: #28a745; color: white; }
    .status-withdrawn { background-color: #6c757d; color: white; }
    .status-transferred { background-color: #fd7e14; color: white; }
    .status-suspended { background-color: #dc3545; color: white; }
    
    .info-row {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 120px;
        margin-left: 10px;
    }
    
    .info-value {
        color: #212529;
        flex: 1;
    }

    /* إصلاح مشكلة ظهور النص في القوائم المنسدلة */
    .form-select option {
        color: #000 !important;
        background-color: #fff !important;
    }

    .form-select {
        color: #000 !important;
        background-color: #fff !important;
    }

    .form-select:focus {
        color: #000 !important;
        background-color: #fff !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-user-graduate"></i>
        إدارة الطلبة
    </h1>
    <p class="page-subtitle">إدارة بيانات الطلبة - عرض وإضافة وتعديل وحذف</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addStudentModal">
        <i class="fas fa-user-plus"></i>
        إضافة طالب جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-secondary" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
        تحديث
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث في الأسماء</label>
                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن طالب..." onkeyup="filterStudents()">
            </div>
            <div class="col-md-2">
                <label class="form-label">الجنس</label>
                <select class="form-select" id="genderFilter" onchange="filterStudents()">
                    <option value="">جميع الأجناس</option>
                    <option value="ذكر">ذكر</option>
                    <option value="أنثى">أنثى</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter" onchange="filterStudents()">
                    <option value="">جميع الحالات</option>
                    <option value="مستمر في السنة التحضيرية">مستمر في السنة التحضيرية</option>
                    <option value="مستمر في السنة البحثية">مستمر في السنة البحثية</option>
                    <option value="خريج">خريج</option>
                    <option value="منسحب">منسحب</option>
                    <option value="منقول">منقول</option>
                    <option value="منقطع">منقطع</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">البرنامج</label>
                <select class="form-select" id="programFilter" onchange="filterStudents()">
                    <option value="">جميع البرامج</option>
                    <!-- سيتم تحميل البرامج ديناميكياً -->
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">السنة الدراسية</label>
                <select class="form-select" id="yearFilter" onchange="filterStudents()">
                    <option value="">جميع السنوات</option>
                    <!-- سيتم تحميل السنوات ديناميكياً -->
                </select>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4" id="statisticsCards">
    <!-- سيتم تحميل الإحصائيات هنا -->
</div>

<!-- Students Container -->
<div id="studentsContainer">
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-3 text-muted">جاري تحميل بيانات الطلبة...</p>
    </div>
</div>

<!-- Add Student Modal -->
<div class="modal fade" id="addStudentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    إضافة طالب جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStudentForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم الطالب *</label>
                            <input type="text" class="form-control" id="name" name="name" required maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">الجنس *</label>
                            <select class="form-select" id="gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="program_id" class="form-label">البرنامج الأكاديمي *</label>
                            <select class="form-select" id="program_id" name="program_id" required style="color: #000 !important; background-color: #fff !important;">
                                <option value="" style="color: #6c757d;">اختر البرنامج</option>
                                <!-- سيتم تحميل البرامج ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="specialization_id" class="form-label">التخصص</label>
                            <select class="form-select" id="specialization_id" name="specialization_id" style="color: #000 !important; background-color: #fff !important;">
                                <option value="" style="color: #6c757d;">اختر التخصص</option>
                                <!-- سيتم تحميل التخصصات ديناميكياً -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="year_id" class="form-label">السنة الدراسية *</label>
                            <select class="form-select" id="year_id" name="year_id" required>
                                <option value="">اختر السنة الدراسية</option>
                                <!-- سيتم تحميل السنوات ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="channel_id" class="form-label">قناة القبول *</label>
                            <select class="form-select" id="channel_id" name="channel_id" required>
                                <option value="">اختر قناة القبول</option>
                                <!-- سيتم تحميل قنوات القبول ديناميكياً -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="status" class="form-label">حالة الطالب *</label>
                            <select class="form-select" id="status" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="مستمر في السنة التحضيرية">مستمر في السنة التحضيرية</option>
                                <option value="مستمر في السنة البحثية">مستمر في السنة البحثية</option>
                                <option value="خريج">خريج</option>
                                <option value="منسحب">منسحب</option>
                                <option value="منقول">منقول</option>
                                <option value="منقطع">منقطع</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="addStudent()">
                    <i class="fas fa-save"></i>
                    حفظ الطالب
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Student Modal -->
<div class="modal fade" id="editStudentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i>
                    تعديل بيانات الطالب
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editStudentForm">
                    <input type="hidden" id="edit_student_id" name="student_id">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_name" class="form-label">اسم الطالب *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_gender" class="form-label">الجنس *</label>
                            <select class="form-select" id="edit_gender" name="gender" required>
                                <option value="">اختر الجنس</option>
                                <option value="ذكر">ذكر</option>
                                <option value="أنثى">أنثى</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_program_id" class="form-label">البرنامج الأكاديمي *</label>
                            <select class="form-select" id="edit_program_id" name="program_id" required style="color: #000 !important; background-color: #fff !important;">
                                <option value="" style="color: #6c757d;">اختر البرنامج</option>
                                <!-- سيتم تحميل البرامج ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_specialization_id" class="form-label">التخصص</label>
                            <select class="form-select" id="edit_specialization_id" name="specialization_id" style="color: #000 !important; background-color: #fff !important;">
                                <option value="" style="color: #6c757d;">اختر التخصص</option>
                                <!-- سيتم تحميل التخصصات ديناميكياً -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_year_id" class="form-label">السنة الدراسية *</label>
                            <select class="form-select" id="edit_year_id" name="year_id" required>
                                <option value="">اختر السنة الدراسية</option>
                                <!-- سيتم تحميل السنوات ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_channel_id" class="form-label">قناة القبول *</label>
                            <select class="form-select" id="edit_channel_id" name="channel_id" required>
                                <option value="">اختر قناة القبول</option>
                                <!-- سيتم تحميل قنوات القبول ديناميكياً -->
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="edit_status" class="form-label">حالة الطالب *</label>
                            <select class="form-select" id="edit_status" name="status" required>
                                <option value="">اختر الحالة</option>
                                <option value="مستمر في السنة التحضيرية">مستمر في السنة التحضيرية</option>
                                <option value="مستمر في السنة البحثية">مستمر في السنة البحثية</option>
                                <option value="خريج">خريج</option>
                                <option value="منسحب">منسحب</option>
                                <option value="منقول">منقول</option>
                                <option value="منقطع">منقطع</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="updateStudent()">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها!
                </div>
                <p>هل أنت متأكد من حذف بيانات الطالب؟</p>
                <div id="deleteStudentInfo" class="bg-light p-3 rounded">
                    <!-- سيتم عرض معلومات الطالب هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let students = [];
let programs = [];
let specializations = [];
let years = [];
let channels = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تحميل صفحة الطلبة...');

    try {
        // تحميل البيانات المرجعية أولاً
        await loadDropdownData();

        // ثم تحميل الطلبة
        await loadStudents();

        console.log('✅ تم تحميل جميع البيانات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);

        // تحميل احتياطي بعد ثانية واحدة
        setTimeout(async function() {
            console.log('🔄 إعادة محاولة تحميل البيانات...');
            try {
                await loadDropdownData();
                await loadStudents();
            } catch (retryError) {
                console.error('❌ فشل في إعادة المحاولة:', retryError);
            }
        }, 1000);
    }
});

// تحميل قائمة الطلبة
async function loadStudents() {
    try {
        console.log('🔄 تحميل الطلبة...');

        // عرض رسالة التحميل
        document.getElementById('studentsContainer').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3 text-muted">جاري تحميل بيانات الطلبة...</p>
            </div>
        `;

        const response = await fetch('/api/students/full');
        console.log('📡 استجابة الطلبة:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 الطلبة المستلمة:', data);

        if (Array.isArray(data) && data.length > 0) {
            students = data;
            console.log('✅ تم تحميل', students.length, 'طالب');
            displayStudents(students);
            updateStatistics();
        } else if (Array.isArray(data) && data.length === 0) {
            console.log('⚠️ لا توجد طلبة في قاعدة البيانات');
            showNoStudents();
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showError('خطأ في تحميل بيانات الطلبة: ' + (data.error || 'بيانات غير صحيحة'));
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الطلبة:', error);
        showError('حدث خطأ أثناء تحميل بيانات الطلبة: ' + error.message);
    }
}

// تحميل بيانات القوائم المنسدلة
async function loadDropdownData() {
    try {
        console.log('🔄 بدء تحميل بيانات القوائم المنسدلة...');

        // تحميل البرامج الأكاديمية
        console.log('📚 تحميل البرامج الأكاديمية...');
        const programsResponse = await fetch('/api/academic_programs');
        if (programsResponse.ok) {
            programs = await programsResponse.json();
            console.log('✅ تم تحميل البرامج:', programs.length, 'برنامج');
            console.log('📋 عينة من البرامج:', programs.slice(0, 2));
            populateDropdown('program_id', programs, 'program_id', 'name');
            populateDropdown('edit_program_id', programs, 'program_id', 'name');
            populateDropdown('programFilter', programs, 'program_id', 'name');
        } else {
            console.error('❌ فشل تحميل البرامج:', programsResponse.status);
        }

        // تحميل التخصصات
        console.log('🎯 تحميل التخصصات...');
        const specializationsResponse = await fetch('/api/specializations');
        if (specializationsResponse.ok) {
            specializations = await specializationsResponse.json();
            console.log('✅ تم تحميل التخصصات:', specializations.length, 'تخصص');
            console.log('📋 عينة من التخصصات:', specializations.slice(0, 2));
            populateDropdown('specialization_id', specializations, 'specialization_id', 'name');
            populateDropdown('edit_specialization_id', specializations, 'specialization_id', 'name');
        } else {
            console.error('❌ فشل تحميل التخصصات:', specializationsResponse.status);
        }

        // تحميل السنوات الدراسية
        console.log('📅 تحميل السنوات الدراسية...');
        const yearsResponse = await fetch('/api/academic_years');
        if (yearsResponse.ok) {
            years = await yearsResponse.json();
            console.log('✅ تم تحميل السنوات:', years.length, 'سنة');
            console.log('📋 عينة من السنوات:', years.slice(0, 2));
            populateDropdown('year_id', years, 'year_id', 'name');
            populateDropdown('edit_year_id', years, 'year_id', 'name');
            populateDropdown('yearFilter', years, 'year_id', 'name');
        } else {
            console.error('❌ فشل تحميل السنوات:', yearsResponse.status);
        }

        // تحميل قنوات القبول
        console.log('🚪 تحميل قنوات القبول...');
        const channelsResponse = await fetch('/api/admission_channels');
        if (channelsResponse.ok) {
            channels = await channelsResponse.json();
            console.log('✅ تم تحميل قنوات القبول:', channels.length, 'قناة');
            console.log('📋 عينة من قنوات القبول:', channels.slice(0, 2));
            populateDropdown('channel_id', channels, 'channel_id', 'name');
            populateDropdown('edit_channel_id', channels, 'channel_id', 'name');
        } else {
            console.error('❌ فشل تحميل قنوات القبول:', channelsResponse.status);
        }

        console.log('✅ انتهى تحميل جميع بيانات القوائم المنسدلة');

    } catch (error) {
        console.error('❌ خطأ في تحميل بيانات القوائم:', error);
    }
}

// ملء القائمة المنسدلة
function populateDropdown(selectId, data, valueField, textField) {
    const select = document.getElementById(selectId);
    if (!select || !Array.isArray(data)) {
        console.log(`⚠️ لا يمكن ملء القائمة ${selectId}:`, !select ? 'العنصر غير موجود' : 'البيانات ليست مصفوفة');
        return;
    }

    console.log(`📋 ملء القائمة ${selectId} بـ ${data.length} عنصر`);

    // الاحتفاظ بالخيار الأول
    const firstOption = select.querySelector('option');
    select.innerHTML = '';
    if (firstOption) {
        select.appendChild(firstOption);
    }

    data.forEach(item => {
        const option = document.createElement('option');
        option.value = item[valueField];
        option.textContent = item[textField];
        option.style.color = '#000';
        option.style.backgroundColor = '#fff';
        select.appendChild(option);
    });

    // التأكد من أن القائمة تظهر بوضوح
    select.style.color = '#000';
    select.style.backgroundColor = '#fff';

    console.log(`✅ تم ملء القائمة ${selectId} بنجاح`);
}

// عرض الطلبة
function displayStudents(studentsToDisplay) {
    console.log('🎨 عرض الطلبة:', studentsToDisplay.length, 'طالب');
    const container = document.getElementById('studentsContainer');

    if (!container) {
        console.error('❌ عنصر studentsContainer غير موجود!');
        return;
    }

    if (!studentsToDisplay || studentsToDisplay.length === 0) {
        console.log('⚠️ لا توجد طلبة للعرض');
        showNoStudents();
        return;
    }

    // التحقق من تحميل البيانات المرجعية
    if (!years || years.length === 0 || !programs || programs.length === 0) {
        console.log('⚠️ لم يتم تحميل الأعوام والبرامج بعد، سيتم إعادة المحاولة...');
        setTimeout(() => displayStudents(studentsToDisplay), 500);
        return;
    }

    // تجميع الطلبة حسب العام الدراسي والبرنامج الأكاديمي
    const studentsByYearAndProgram = {};

    studentsToDisplay.forEach(student => {
        const yearName = student.year_name || 'غير محدد العام';
        const programName = student.program_name || 'غير محدد البرنامج';
        const groupKey = `${yearName} - ${programName}`;

        if (!studentsByYearAndProgram[groupKey]) {
            studentsByYearAndProgram[groupKey] = {
                yearName: yearName,
                programName: programName,
                students: []
            };
        }

        studentsByYearAndProgram[groupKey].students.push(student);
    });

    let html = '';

    // ترتيب المجموعات
    const sortedGroups = Object.keys(studentsByYearAndProgram).sort();

    sortedGroups.forEach((groupKey, index) => {
        const groupData = studentsByYearAndProgram[groupKey];
        const studentCount = groupData.students.length;
        const collapseId = `group-${index}`;

        html += `
            <div class="card mb-3">
                <div class="card-header" id="heading-${index}">
                    <h5 class="mb-0">
                        <button class="btn btn-link text-decoration-none w-100 text-start d-flex justify-content-between align-items-center"
                                type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                                aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                            <span>
                                <i class="fas fa-graduation-cap me-2"></i>
                                ${groupData.yearName} - ${groupData.programName}
                                <span class="badge bg-success ms-2">${studentCount}</span>
                            </span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </h5>
                </div>

                <div id="${collapseId}" class="collapse ${index === 0 ? 'show' : ''}"
                     aria-labelledby="heading-${index}">
                    <div class="card-body">
                        <div class="row">
        `;

        // عرض الطلبة في هذه المجموعة
        groupData.students.forEach(student => {
            const genderClass = getGenderClass(student.gender);
            const statusClass = getStatusClass(student.status);
            const createdDate = student.created_date ? new Date(student.created_date).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) : 'غير محدد';

            const specializationName = student.specialization_name;
            const channelName = student.channel_name;

            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="student-card">
                        <div class="student-header">
                            <div>
                                <h6 class="mb-1">${student.name}</h6>
                                <small>معرف الطالب: ${student.student_id}</small>
                            </div>
                            <div class="text-left">
                                <span class="gender-badge ${genderClass}">${student.gender}</span>
                            </div>
                        </div>
                        <div class="student-body">
                            <div class="info-row">
                                <span class="info-label">الحالة:</span>
                                <span class="info-value">
                                    <span class="status-badge ${statusClass}">${student.status}</span>
                                </span>
                            </div>
                            ${specializationName ? `
                            <div class="info-row">
                                <span class="info-label">التخصص:</span>
                                <span class="info-value">${specializationName}</span>
                            </div>
                            ` : ''}
                            <div class="info-row">
                                <span class="info-label">قناة القبول:</span>
                                <span class="info-value">${channelName || 'غير محدد'}</span>
                            </div>
                            <div class="info-row">
                                <span class="info-label">تاريخ الإضافة:</span>
                                <span class="info-value">${createdDate}</span>
                            </div>

                            <div class="d-flex gap-2 mt-3">
                                <button class="btn btn-sm btn-outline-primary" onclick="editStudent(${student.student_id})">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteStudent(${student.student_id})">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// فلترة الطلبة
function filterStudents() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const genderFilter = document.getElementById('genderFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const programFilter = document.getElementById('programFilter').value;
    const yearFilter = document.getElementById('yearFilter').value;

    const filteredStudents = students.filter(student => {
        const matchesSearch = student.name.toLowerCase().includes(searchTerm);
        const matchesGender = !genderFilter || student.gender === genderFilter;
        const matchesStatus = !statusFilter || student.status === statusFilter;
        const matchesProgram = !programFilter || student.program_id == programFilter;
        const matchesYear = !yearFilter || student.year_id == yearFilter;

        return matchesSearch && matchesGender && matchesStatus && matchesProgram && matchesYear;
    });

    displayStudents(filteredStudents);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('genderFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('programFilter').value = '';
    document.getElementById('yearFilter').value = '';

    displayStudents(students);
}

// إضافة طالب جديد
function addStudent() {
    const form = document.getElementById('addStudentForm');
    const formData = new FormData(form);

    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    console.log('📤 إضافة طالب جديد:', data);

    fetch('/api/students', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('📡 استجابة إضافة الطالب:', response.status);
        return response.json();
    })
    .then(result => {
        console.log('📊 نتيجة إضافة الطالب:', result);
        if (result.error) {
            showAlert('خطأ في إضافة الطالب: ' + result.error, 'error');
        } else {
            showAlert('تم إضافة الطالب بنجاح', 'success');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('addStudentModal')).hide();
            loadStudents();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في إضافة الطالب:', error);
        showAlert('حدث خطأ أثناء إضافة الطالب: ' + error.message, 'error');
    });
}

// تعديل طالب
function editStudent(studentId) {
    const student = students.find(s => s.student_id === studentId);
    if (!student) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('edit_student_id').value = student.student_id;
    document.getElementById('edit_name').value = student.name;
    document.getElementById('edit_gender').value = student.gender;
    document.getElementById('edit_program_id').value = student.program_id;
    document.getElementById('edit_specialization_id').value = student.specialization_id || '';
    document.getElementById('edit_year_id').value = student.year_id;
    document.getElementById('edit_channel_id').value = student.channel_id;
    document.getElementById('edit_status').value = student.status;

    new bootstrap.Modal(document.getElementById('editStudentModal')).show();
}

// تحديث بيانات الطالب
function updateStudent() {
    const form = document.getElementById('editStudentForm');
    const formData = new FormData(form);
    const studentId = formData.get('student_id');

    const data = {};
    formData.forEach((value, key) => {
        if (key !== 'student_id') {
            data[key] = value;
        }
    });

    console.log('📤 تحديث الطالب:', studentId, data);

    fetch(`/api/students/${studentId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            showAlert('خطأ في تحديث الطالب: ' + result.error, 'error');
        } else {
            showAlert('تم تحديث بيانات الطالب بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editStudentModal')).hide();
            loadStudents();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تحديث الطالب:', error);
        showAlert('حدث خطأ أثناء تحديث الطالب: ' + error.message, 'error');
    });
}

// تأكيد حذف الطالب
function deleteStudent(studentId) {
    const student = students.find(s => s.student_id === studentId);
    if (!student) return;

    const genderClass = getGenderClass(student.gender);
    const statusClass = getStatusClass(student.status);

    document.getElementById('deleteStudentInfo').innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-user-graduate me-2"></i>
            <div>
                <strong>${student.name}</strong><br>
                <small class="text-muted">رقم الطالب: ${student.student_id}</small><br>
                <span class="gender-badge ${genderClass}">${student.gender}</span>
                <span class="status-badge ${statusClass}">${student.status}</span>
            </div>
        </div>
    `;

    // تخزين ID الطالب للحذف
    document.getElementById('confirmDeleteBtn').setAttribute('data-student-id', studentId);

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// حذف الطالب
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        const studentId = this.getAttribute('data-student-id');
        if (!studentId) return;

        fetch(`/api/students/${studentId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                showAlert(result.error, 'error');
            } else {
                showAlert('تم حذف الطالب بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                loadStudents();
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ أثناء حذف الطالب', 'error');
        });
    });
});

function refreshData() {
    loadStudents();
}

function exportData() {
    // تصدير البيانات إلى Excel
    const csvContent = "data:text/csv;charset=utf-8,"
        + "رقم الطالب,اسم الطالب,الجنس,الحالة,البرنامج,التخصص,السنة الدراسية,قناة القبول,تاريخ الإنشاء\n"
        + students.map(student => {
            const program = programs.find(p => p.program_id === student.program_id);
            const specialization = specializations.find(s => s.specialization_id === student.specialization_id);
            const year = years.find(y => y.year_id === student.year_id);
            const channel = channels.find(c => c.channel_id === student.channel_id);

            console.log(`🔍 طالب ${student.name}:`, {
                program_id: student.program_id,
                program_found: program,
                specialization_id: student.specialization_id,
                specialization_found: specialization
            });

            return `${student.student_id},"${student.name}","${student.gender}","${student.status}","${program ? program.name : ''}","${specialization ? specialization.name : ''}","${year ? year.name : ''}","${channel ? channel.name : ''}","${student.created_date || ''}"`;
        }).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "students_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    // إنشاء تنبيه Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('📊 تحديث الإحصائيات...');

    if (!students || students.length === 0) {
        console.log('⚠️ لا توجد بيانات طلبة للإحصائيات');
        document.getElementById('statisticsCards').innerHTML = '';
        return;
    }

    const totalStudents = students.length;
    const maleStudents = students.filter(s => s.gender === 'ذكر').length;
    const femaleStudents = students.filter(s => s.gender === 'أنثى').length;

    const prepStudents = students.filter(s => s.status === 'مستمر في السنة التحضيرية').length;
    const researchStudents = students.filter(s => s.status === 'مستمر في السنة البحثية').length;
    const graduateStudents = students.filter(s => s.status === 'خريج').length;

    console.log(`📊 إحصائيات: إجمالي=${totalStudents}, ذكور=${maleStudents}, إناث=${femaleStudents}`);

    const html = `
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${totalStudents}</h4>
                            <p class="mb-0">إجمالي الطلبة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${maleStudents}</h4>
                            <p class="mb-0">الطلبة الذكور</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-male fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${femaleStudents}</h4>
                            <p class="mb-0">الطالبات الإناث</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-female fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${graduateStudents}</h4>
                            <p class="mb-0">الخريجين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-graduation-cap fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('statisticsCards').innerHTML = html;
}

// دوال المساعدة
function getGenderClass(gender) {
    switch(gender) {
        case 'ذكر': return 'gender-male';
        case 'أنثى': return 'gender-female';
        default: return 'gender-male';
    }
}

function getStatusClass(status) {
    switch(status) {
        case 'مستمر في السنة التحضيرية': return 'status-prep';
        case 'مستمر في السنة البحثية': return 'status-research';
        case 'خريج': return 'status-graduate';
        case 'منسحب': return 'status-withdrawn';
        case 'منقول': return 'status-transferred';
        case 'منقطع': return 'status-suspended';
        default: return 'status-prep';
    }
}

// عرض رسالة خطأ
function showError(message) {
    document.getElementById('studentsContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
            </div>
            <h4 class="text-danger">خطأ في تحميل البيانات</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadStudents()">
                <i class="fas fa-sync-alt"></i>
                إعادة المحاولة
            </button>
        </div>
    `;
}

// عرض رسالة عدم وجود طلبة
function showNoStudents() {
    document.getElementById('studentsContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-muted mb-4">
                <i class="fas fa-user-graduate fa-3x"></i>
            </div>
            <h4 class="text-muted">لا توجد طلبة</h4>
            <p class="text-muted">لم يتم تسجيل أي طلبة في النظام بعد</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                <i class="fas fa-user-plus"></i>
                إضافة أول طالب
            </button>
        </div>
    `;
}
</script>
{% endblock %}
