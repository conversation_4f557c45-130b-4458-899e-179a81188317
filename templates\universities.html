{% extends "base.html" %}

{% block title %}إدارة الجامعات - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-university"></i>
        إدارة الجامعات
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات الجامعات</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUniversityModal">
        <i class="fas fa-plus"></i>
        إضافة جامعة جديدة
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الجامعات...">
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Universities Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="universitiesTable">
                <thead>
                    <tr>
                        <th>الشعار</th>
                        <th>الاسم بالعربية</th>
                        <th>الاسم بالإنجليزية</th>
                        <th>العنوان</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="universitiesTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add University Modal -->
<div class="modal fade" id="addUniversityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة جامعة جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUniversityForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nameAr" class="form-label">الاسم بالعربية *</label>
                                <input type="text" class="form-control" id="nameAr" name="name_ar" maxlength="255" required>
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nameEn" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" class="form-control" id="nameEn" name="name_en" maxlength="255">
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" maxlength="500"></textarea>
                        <div class="form-text">الحد الأقصى 500 حرف</div>
                    </div>
                    <div class="mb-3">
                        <label for="logo" class="form-label">شعار الجامعة</label>
                        <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                        <div class="form-text">يُحفظ المسار في logo_path</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addUniversity()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit University Modal -->
<div class="modal fade" id="editUniversityModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل بيانات الجامعة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUniversityForm">
                    <input type="hidden" id="editUniversityId" name="university_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editNameAr" class="form-label">الاسم بالعربية *</label>
                                <input type="text" class="form-control" id="editNameAr" name="name_ar" maxlength="255" required>
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editNameEn" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" class="form-control" id="editNameEn" name="name_en" maxlength="255">
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editAddress" class="form-label">العنوان</label>
                        <textarea class="form-control" id="editAddress" name="address" rows="3" maxlength="500"></textarea>
                        <div class="form-text">الحد الأقصى 500 حرف</div>
                    </div>
                    <div class="mb-3">
                        <label for="editLogo" class="form-label">شعار الجامعة</label>
                        <div id="currentLogo" class="mb-2" style="display: none;">
                            <label class="form-label">الشعار الحالي:</label>
                            <div>
                                <img id="currentLogoImg" src="" alt="الشعار الحالي" style="width: 80px; height: 80px; object-fit: cover; border-radius: 5px; border: 1px solid #ddd;">
                            </div>
                        </div>
                        <input type="file" class="form-control" id="editLogo" name="logo" accept="image/*">
                        <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير الشعار</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateUniversity()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<script>
let universities = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadUniversities();
});

// تحميل قائمة الجامعات
function loadUniversities() {
    fetch('/api/universities')
        .then(response => response.json())
        .then(data => {
            universities = data;
            displayUniversities(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showErrorModal('خطأ في تحميل بيانات الجامعات', 'خطأ في التحميل!');
        });
}

// عرض الجامعات في الجدول
function displayUniversities(data) {
    const tbody = document.getElementById('universitiesTableBody');
    tbody.innerHTML = '';

    data.forEach(university => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                ${university.has_logo ?
                    `<img src="/api/universities/${university.university_id}/logo" alt="شعار الجامعة" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                     <i class="fas fa-university fa-2x text-muted" style="display: none;"></i>` :
                    '<i class="fas fa-university fa-2x text-muted"></i>'
                }
            </td>
            <td>${university.name_ar}</td>
            <td>${university.name_en || '-'}</td>
            <td>${university.address || '-'}</td>
            <td>${university.created_date || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editUniversity(${university.university_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteUniversity(${university.university_id}, '${university.name_ar}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// إضافة جامعة جديدة
function addUniversity() {
    const form = document.getElementById('addUniversityForm');

    // التحقق من صحة البيانات
    const nameAr = form.querySelector('#nameAr').value.trim();
    if (!nameAr) {
        showWarningModal('اسم الجامعة بالعربية مطلوب', 'بيانات مطلوبة!');
        return;
    }

    const formData = new FormData(form);

    console.log('إرسال البيانات:', {
        name_ar: formData.get('name_ar'),
        name_en: formData.get('name_en'),
        address: formData.get('address'),
        logo: formData.get('logo') ? formData.get('logo').name : 'لا يوجد'
    });

    fetch('/api/universities', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('استجابة الخادم:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('بيانات الاستجابة:', data);
        if (data.message) {
            showSaveSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('addUniversityModal')).hide();
            form.reset();
            loadUniversities();
        } else {
            showSaveError(data.error || 'حدث خطأ غير متوقع');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showSaveError('خطأ في إضافة الجامعة');
    });
}

// تعديل جامعة
function editUniversity(universityId) {
    const university = universities.find(u => u.university_id === universityId);
    if (!university) return;

    document.getElementById('editUniversityId').value = university.university_id;
    document.getElementById('editNameAr').value = university.name_ar;
    document.getElementById('editNameEn').value = university.name_en || '';
    document.getElementById('editAddress').value = university.address || '';

    // عرض الصورة الحالية إذا كانت موجودة
    const currentLogoDiv = document.getElementById('currentLogo');
    const currentLogoImg = document.getElementById('currentLogoImg');

    if (university.has_logo) {
        currentLogoImg.src = `/api/universities/${university.university_id}/logo`;
        currentLogoDiv.style.display = 'block';
    } else {
        currentLogoDiv.style.display = 'none';
    }

    new bootstrap.Modal(document.getElementById('editUniversityModal')).show();
}

// تحديث بيانات الجامعة
function updateUniversity() {
    const form = document.getElementById('editUniversityForm');
    const formData = new FormData(form);
    const universityId = document.getElementById('editUniversityId').value;

    fetch(`/api/universities/${universityId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showUpdateSuccess(data.message);
            bootstrap.Modal.getInstance(document.getElementById('editUniversityModal')).hide();
            loadUniversities();
        } else {
            showUpdateError(data.error || 'حدث خطأ غير متوقع');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showUpdateError('خطأ في تحديث الجامعة');
    });
}

// حذف جامعة
function deleteUniversity(universityId, universityName) {
    if (!confirm(`هل أنت متأكد من حذف الجامعة "${universityName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/universities/${universityId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showDeleteSuccess(data.message);
            loadUniversities();
        } else {
            showDeleteError(data.error || 'حدث خطأ غير متوقع');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showDeleteError('خطأ في حذف الجامعة');
    });
}

// البحث في الجامعات
document.getElementById('searchInput').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const filteredData = universities.filter(university => 
        university.name_ar.toLowerCase().includes(searchTerm) ||
        (university.name_en && university.name_en.toLowerCase().includes(searchTerm)) ||
        (university.address && university.address.toLowerCase().includes(searchTerm))
    );
    displayUniversities(filteredData);
});

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    displayUniversities(universities);
}

// تصدير البيانات
function exportData() {
    // يمكن تطوير هذه الوظيفة لاحقاً
    showInfoModal('سيتم تطوير وظيفة التصدير قريباً', 'قريباً!');
}

// طباعة البيانات
function printData() {
    window.print();
}


</script>
{% endblock %}
