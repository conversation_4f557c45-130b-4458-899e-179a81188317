{% extends "base.html" %}

{% block title %}إدارة قنوات القبول - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    /* تحسين مظهر الجدول */
    .table-dark th {
        background-color: #343a40 !important;
        color: #fff !important;
        font-weight: 600;
        border-bottom: 2px solid #495057;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
        transition: background-color 0.15s ease-in-out;
    }

    .btn-group .btn {
        margin: 0 2px;
    }

    .fw-semibold {
        font-weight: 600;
    }

    .text-primary {
        color: #0d6efd !important;
    }

    .text-muted {
        color: #6c757d !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-door-open"></i>
        إدارة قنوات القبول
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات قنوات القبول</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addChannelModal">
        <i class="fas fa-plus"></i>
        إضافة قناة قبول جديدة
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في قنوات القبول...">
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Admission Channels Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="channelsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>قناة القبول</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="channelsTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Channel Modal -->
<div class="modal fade" id="addChannelModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة قناة قبول جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="channelForm">
                    <input type="hidden" id="channelId" name="channel_id">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">قناة القبول *</label>
                        <input type="text" class="form-control" id="name" name="name" required maxlength="255" 
                               placeholder="مثال: القبول العام، المنافسة، المنح الدراسية">
                        <div class="form-text">أدخل اسم قناة القبول</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveChannel()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let admissionChannels = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAdmissionChannels();
});

// تحميل قائمة قنوات القبول
async function loadAdmissionChannels() {
    try {
        console.log('🔄 بدء تحميل قنوات القبول...');

        const response = await fetch('/api/admission_channels');
        console.log('📡 استجابة قنوات القبول:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 قنوات القبول المستلمة:', data);
        console.log('📊 نوع البيانات:', typeof data);
        console.log('📊 هل هي مصفوفة؟', Array.isArray(data));

        if (Array.isArray(data)) {
            admissionChannels = data;
            console.log('✅ تم تحميل', data.length, 'قناة قبول');
            displayAdmissionChannels(data);
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showAlert('خطأ في تحميل بيانات قنوات القبول: ' + (data.error || 'بيانات غير صحيحة'), 'error');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل قنوات القبول:', error);
        showAlert('حدث خطأ أثناء تحميل بيانات قنوات القبول: ' + error.message, 'error');
    }
}

// عرض قنوات القبول في الجدول
function displayAdmissionChannels(data) {
    console.log('🎨 عرض قنوات القبول:', data.length, 'قناة');
    const tbody = document.getElementById('channelsTableBody');

    if (!tbody) {
        console.error('❌ عنصر الجدول غير موجود!');
        return;
    }

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-door-open fa-3x mb-3"></i>
                        <h5>لا توجد قنوات قبول مسجلة حالياً</h5>
                        <p>يمكنك إضافة أول قناة قبول باستخدام الزر أعلاه</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((channel, index) => {
        console.log(`📋 قناة ${index + 1}:`, channel);

        const row = document.createElement('tr');
        const createdDate = channel.created_date ?
            new Date(channel.created_date).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) : '-';

        const channelName = channel.name || 'غير محدد';
        console.log(`📝 اسم القناة: "${channelName}"`);

        row.innerHTML = `
            <td class="text-center fw-bold">${index + 1}</td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-door-open text-primary me-2"></i>
                    <span class="fw-semibold">${channelName}</span>
                </div>
            </td>
            <td>
                <small class="text-muted">
                    <i class="fas fa-calendar-alt me-1"></i>
                    ${createdDate}
                </small>
            </td>
            <td class="text-center">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editChannel(${channel.channel_id})" title="تعديل قناة القبول">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteChannel(${channel.channel_id}, '${channelName.replace(/'/g, "\\'")}'))" title="حذف قناة القبول">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    console.log('✅ تم عرض', data.length, 'قناة قبول بنجاح');
}

// إضافة أو تحديث قناة قبول
function saveChannel() {
    const form = document.getElementById('channelForm');
    const channelId = document.getElementById('channelId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();

    if (!name) {
        showAlert('اسم قناة القبول مطلوب', 'error');
        return;
    }

    const data = {
        name: name
    };

    const url = channelId ? `/api/admission_channels/${channelId}` : '/api/admission_channels';
    const method = channelId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addChannelModal')).hide();
            form.reset();
            document.getElementById('channelId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة قناة قبول جديدة';
            loadAdmissionChannels();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ قناة القبول', 'error');
    });
}

// تعديل قناة قبول
function editChannel(channelId) {
    const channel = admissionChannels.find(c => c.channel_id === channelId);
    if (!channel) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('channelId').value = channel.channel_id;
    document.getElementById('name').value = channel.name;
    document.getElementById('modalTitle').textContent = 'تعديل بيانات قناة القبول';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addChannelModal')).show();
}

// حذف قناة قبول
function deleteChannel(channelId, channelName) {
    if (!confirm(`هل أنت متأكد من حذف قناة القبول "${channelName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/admission_channels/${channelId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadAdmissionChannels();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف قناة القبول', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterChannels();
});

function filterChannels() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    const filteredData = admissionChannels.filter(channel => {
        return channel.name.toLowerCase().includes(searchTerm);
    });

    displayAdmissionChannels(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    displayAdmissionChannels(admissionChannels);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addChannelModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('channelForm').reset();
            document.getElementById('channelId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة قناة قبول جديدة';
        });
    }
});
</script>
{% endblock %}
