import json
import os
import pyodbc
import logging
from datetime import datetime

# Default configuration
DEFAULT_CONFIG = {
    'server': 'HUSSEIN\\SQLEXPRESS',
    'database': 'PGSMS',
    'driver': '{ODBC Driver 17 for SQL Server}',
    'authentication': 'windows',
    'username': '',
    'password': ''
}

CONFIG_FILE = 'database_config.json'

class DatabaseConfig:
    def __init__(self):
        self.config = self.load_config()
    
    def load_config(self):
        """تحميل إعدادات قاعدة البيانات من الملف"""
        try:
            if os.path.exists(CONFIG_FILE):
                with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # دمج مع الإعدادات الافتراضية
                    return {**DEFAULT_CONFIG, **config}
            else:
                return DEFAULT_CONFIG.copy()
        except Exception as e:
            logging.error(f"خطأ في تحميل إعدادات قاعدة البيانات: {str(e)}")
            return DEFAULT_CONFIG.copy()
    
    def save_config(self, new_config):
        """حفظ إعدادات قاعدة البيانات"""
        try:
            # تحديث الإعدادات
            self.config.update(new_config)
            
            # حفظ في الملف
            with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            logging.error(f"خطأ في حفظ إعدادات قاعدة البيانات: {str(e)}")
            return False
    
    def get_connection_string(self):
        """إنشاء نص الاتصال بقاعدة البيانات"""
        try:
            if self.config['authentication'] == 'windows':
                connection_string = (
                    f"DRIVER={self.config['driver']};"
                    f"SERVER={self.config['server']};"
                    f"DATABASE={self.config['database']};"
                    f"Trusted_Connection=yes;"
                )
            else:
                connection_string = (
                    f"DRIVER={self.config['driver']};"
                    f"SERVER={self.config['server']};"
                    f"DATABASE={self.config['database']};"
                    f"UID={self.config['username']};"
                    f"PWD={self.config['password']};"
                )
            
            return connection_string
        except Exception as e:
            logging.error(f"خطأ في إنشاء نص الاتصال: {str(e)}")
            return None
    
    def get_server_connection_string(self):
        """إنشاء نص الاتصال بالخادم فقط (بدون قاعدة بيانات محددة)"""
        try:
            if self.config['authentication'] == 'windows':
                connection_string = (
                    f"DRIVER={self.config['driver']};"
                    f"SERVER={self.config['server']};"
                    f"Trusted_Connection=yes;"
                )
            else:
                connection_string = (
                    f"DRIVER={self.config['driver']};"
                    f"SERVER={self.config['server']};"
                    f"UID={self.config['username']};"
                    f"PWD={self.config['password']};"
                )
            
            return connection_string
        except Exception as e:
            logging.error(f"خطأ في إنشاء نص الاتصال بالخادم: {str(e)}")
            return None
    
    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            connection_string = self.get_connection_string()
            if not connection_string:
                return False, "فشل في إنشاء نص الاتصال"
            
            conn = pyodbc.connect(connection_string, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return True, "تم الاتصال بنجاح"
            else:
                return False, "فشل في تنفيذ الاستعلام"
                
        except pyodbc.Error as e:
            error_msg = str(e)
            if "Login failed" in error_msg:
                return False, "فشل في تسجيل الدخول - تحقق من اسم المستخدم وكلمة المرور"
            elif "server was not found" in error_msg.lower():
                return False, "لم يتم العثور على الخادم - تحقق من اسم الخادم"
            elif "database" in error_msg.lower() and "does not exist" in error_msg.lower():
                return False, "قاعدة البيانات غير موجودة - قم بإنشائها أولاً"
            else:
                return False, f"خطأ في الاتصال: {error_msg}"
        except Exception as e:
            return False, f"خطأ غير متوقع: {str(e)}"
    
    def test_server_connection(self):
        """اختبار الاتصال بالخادم فقط"""
        try:
            connection_string = self.get_server_connection_string()
            if not connection_string:
                return False, "فشل في إنشاء نص الاتصال"
            
            conn = pyodbc.connect(connection_string, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result:
                return True, "تم الاتصال بالخادم بنجاح"
            else:
                return False, "فشل في تنفيذ الاستعلام"
                
        except Exception as e:
            return False, f"خطأ في الاتصال بالخادم: {str(e)}"
    
    def create_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        try:
            # أولاً اختبار الاتصال بالخادم
            server_success, server_msg = self.test_server_connection()
            if not server_success:
                return False, f"فشل الاتصال بالخادم: {server_msg}"
            
            # الاتصال بالخادم لإنشاء قاعدة البيانات
            connection_string = self.get_server_connection_string()
            conn = pyodbc.connect(connection_string)
            conn.autocommit = True
            cursor = conn.cursor()
            
            # إنشاء قاعدة البيانات إذا لم تكن موجودة
            database_name = self.config['database']
            cursor.execute(f"""
            IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = '{database_name}')
            CREATE DATABASE [{database_name}]
            """)
            
            conn.close()
            
            # الآن إنشاء الجداول
            from database import create_database_and_tables
            success = create_database_and_tables()
            
            if success:
                return True, "تم إنشاء قاعدة البيانات والجداول بنجاح"
            else:
                return False, "فشل في إنشاء الجداول"
                
        except Exception as e:
            return False, f"خطأ في إنشاء قاعدة البيانات: {str(e)}"
    
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        try:
            connection_string = self.get_connection_string()
            if not connection_string:
                raise Exception("فشل في إنشاء نص الاتصال")
            
            conn = pyodbc.connect(connection_string)
            return conn
        except Exception as e:
            logging.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            raise
    
    def get_system_info(self):
        """الحصول على معلومات النظام وقاعدة البيانات"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()
            
            info = {}
            
            # عدد الجداول
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_TYPE = 'BASE TABLE'
            """)
            info['table_count'] = cursor.fetchone()[0]
            
            # حجم قاعدة البيانات
            cursor.execute(f"""
                SELECT 
                    SUM(size) * 8.0 / 1024 as size_mb
                FROM sys.master_files 
                WHERE database_id = DB_ID('{self.config['database']}')
            """)
            result = cursor.fetchone()
            if result and result[0]:
                info['db_size'] = f"{result[0]:.2f} MB"
            else:
                info['db_size'] = "غير معروف"
            
            # تاريخ آخر تحديث
            info['last_update'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            conn.close()
            return info
            
        except Exception as e:
            logging.error(f"خطأ في الحصول على معلومات النظام: {str(e)}")
            return {
                'table_count': 0,
                'db_size': 'غير معروف',
                'last_update': 'غير معروف'
            }
    
    def optimize_database(self):
        """تحسين قاعدة البيانات"""
        try:
            conn = self.get_connection()
            conn.autocommit = True  # تفعيل الالتزام التلقائي
            cursor = conn.cursor()

            # تحديث الإحصائيات
            cursor.execute("EXEC sp_updatestats")

            # إعادة بناء الفهارس للجداول الرئيسية
            tables = ['users', 'students', 'faculty', 'system_log']
            for table in tables:
                try:
                    cursor.execute(f"ALTER INDEX ALL ON {table} REBUILD")
                except:
                    # إذا فشل إعادة البناء، جرب إعادة التنظيم
                    try:
                        cursor.execute(f"ALTER INDEX ALL ON {table} REORGANIZE")
                    except:
                        pass  # تجاهل الأخطاء للجداول التي لا تحتوي على فهارس

            conn.close()

            return True, "تم تحسين قاعدة البيانات بنجاح"

        except Exception as e:
            return False, f"خطأ في تحسين قاعدة البيانات: {str(e)}"

# إنشاء مثيل عام
db_config = DatabaseConfig()

def get_db_connection():
    """دالة للحصول على اتصال بقاعدة البيانات"""
    return db_config.get_connection()

def test_connection():
    """دالة لاختبار الاتصال"""
    success, message = db_config.test_connection()
    return success
