#!/usr/bin/env python
import os
import sys

# إضافة المجلد الحالي إلى مسار Python
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# تغيير مجلد العمل إلى مجلد الملف
os.chdir(current_dir)

print(f"Starting server from: {current_dir}")
print("Loading app...")

try:
    from app import app
    print("App loaded successfully!")
    print("Starting Flask server on http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)
except Exception as e:
    print(f"Error starting server: {e}")
    import traceback
    traceback.print_exc()
    input("Press Enter to exit...")
