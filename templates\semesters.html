{% extends "base.html" %}

{% block title %}إدارة الفصول الدراسية - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-calendar-week"></i>
        إدارة الفصول الدراسية
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات الفصول الدراسية</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSemesterModal">
        <i class="fas fa-plus"></i>
        إضافة فصل دراسي جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الفصول الدراسية...">
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Semesters Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="semestersTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>الفصل الدراسي</th>
                        <th>العام الدراسي</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="semestersTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Semester Modal -->
<div class="modal fade" id="addSemesterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة فصل دراسي جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="semesterForm">
                    <input type="hidden" id="semesterId" name="semester_id">

                    <div class="mb-3">
                        <label for="yearId" class="form-label">العام الدراسي *</label>
                        <select class="form-select" id="yearId" name="year_id" required>
                            <option value="">اختر العام الدراسي</option>
                        </select>
                        <div class="form-text">اختر العام الدراسي للفصل</div>
                    </div>

                    <div class="mb-3">
                        <label for="name" class="form-label">الفصل الدراسي *</label>
                        <input type="text" class="form-control" id="name" name="name" required maxlength="100"
                               placeholder="مثال: الفصل الأول، الفصل الثاني، الفصل الصيفي">
                        <div class="form-text">أدخل اسم الفصل الدراسي</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSemester()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let semesters = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSemesters();
    loadAcademicYears();
});

// تحميل قائمة الفصول الدراسية
function loadSemesters() {
    fetch('/api/semesters')
        .then(response => response.json())
        .then(data => {
            semesters = data;
            displaySemesters(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showAlert('خطأ في تحميل البيانات', 'error');
        });
}

// عرض الفصول الدراسية في الجدول
function displaySemesters(data) {
    const tbody = document.getElementById('semestersTableBody');
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-4">
                    <i class="fas fa-calendar-week fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد فصول دراسية مسجلة حالياً</p>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((semester, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${semester.name}</td>
            <td>${semester.year_name || '-'}</td>
            <td>${semester.created_date ? new Date(semester.created_date).toLocaleDateString('en-GB') : '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editSemester(${semester.semester_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteSemester(${semester.semester_id}, '${semester.name}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحميل قائمة الأعوام الدراسية
function loadAcademicYears() {
    fetch('/api/academic_years')
        .then(response => response.json())
        .then(data => {
            const yearSelect = document.getElementById('yearId');
            yearSelect.innerHTML = '<option value="">اختر العام الدراسي (اختياري)</option>';

            data.forEach(year => {
                const option = document.createElement('option');
                option.value = year.year_id;
                option.textContent = year.name;
                yearSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الأعوام الدراسية:', error);
        });
}

// إضافة أو تحديث فصل دراسي
function saveSemester() {
    const form = document.getElementById('semesterForm');
    const semesterId = document.getElementById('semesterId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();
    const yearId = form.querySelector('#yearId').value;

    if (!name) {
        showAlert('اسم الفصل الدراسي مطلوب', 'error');
        return;
    }

    if (!yearId) {
        showAlert('العام الدراسي مطلوب', 'error');
        return;
    }

    const data = {
        name: name,
        year_id: yearId
    };

    const url = semesterId ? `/api/semesters/${semesterId}` : '/api/semesters';
    const method = semesterId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addSemesterModal')).hide();
            form.reset();
            document.getElementById('semesterId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة فصل دراسي جديد';
            loadSemesters();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ الفصل الدراسي', 'error');
    });
}

// تعديل فصل دراسي
function editSemester(semesterId) {
    const semester = semesters.find(s => s.semester_id === semesterId);
    if (!semester) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('semesterId').value = semester.semester_id;
    document.getElementById('name').value = semester.name;
    document.getElementById('yearId').value = semester.year_id || '';
    document.getElementById('modalTitle').textContent = 'تعديل بيانات الفصل الدراسي';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addSemesterModal')).show();
}

// حذف فصل دراسي
function deleteSemester(semesterId, semesterName) {
    if (!confirm(`هل أنت متأكد من حذف الفصل الدراسي "${semesterName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/semesters/${semesterId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadSemesters();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف الفصل الدراسي', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterSemesters();
});

function filterSemesters() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    const filteredData = semesters.filter(semester => {
        return semester.name.toLowerCase().includes(searchTerm);
    });

    displaySemesters(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    displaySemesters(semesters);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addSemesterModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('semesterForm').reset();
            document.getElementById('semesterId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة فصل دراسي جديد';
        });
    }
});
</script>
{% endblock %}