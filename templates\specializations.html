{% extends "base.html" %}

{% block title %}إدارة التخصصات - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    /* تحسين مظهر الجدول */
    .table-dark th {
        background-color: #343a40 !important;
        color: #fff !important;
        font-weight: 600;
        border-bottom: 2px solid #495057;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
        transition: background-color 0.15s ease-in-out;
    }

    .btn-group .btn {
        margin: 0 2px;
    }

    .fw-semibold {
        font-weight: 600;
    }

    /* تحسين الأيقونات */
    .text-primary {
        color: #0d6efd !important;
    }

    .text-muted {
        color: #6c757d !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-tags"></i>
        إدارة التخصصات
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات التخصصات الأكاديمية</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSpecializationModal">
        <i class="fas fa-plus"></i>
        إضافة تخصص جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في التخصصات...">
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Specializations Table -->
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">قائمة التخصصات</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover table-striped">
                <thead class="table-dark">
                    <tr>
                        <th scope="col" style="width: 10%; text-align: center;">#</th>
                        <th scope="col" style="width: 50%;">
                            <i class="fas fa-tag me-2"></i>
                            اسم التخصص
                        </th>
                        <th scope="col" style="width: 25%;">
                            <i class="fas fa-calendar me-2"></i>
                            تاريخ الإضافة
                        </th>
                        <th scope="col" style="width: 15%; text-align: center;">
                            <i class="fas fa-cogs me-2"></i>
                            الإجراءات
                        </th>
                    </tr>
                </thead>
                <tbody id="specializationsTableBody">
                    <tr>
                        <td colspan="4" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">جاري التحميل...</span>
                            </div>
                            <p class="mt-3 text-muted">جاري تحميل بيانات التخصصات...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="text-center py-5" id="noDataMessage" style="display: none;">
            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">لا توجد تخصصات مسجلة حالياً</h5>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSpecializationModal">
                إضافة أول تخصص
            </button>
        </div>
    </div>
</div>

<!-- Add/Edit Specialization Modal -->
<div class="modal fade" id="addSpecializationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة تخصص جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="specializationForm">
                    <input type="hidden" id="specializationId" name="specialization_id">

                    <div class="mb-3">
                        <label for="name" class="form-label">اسم التخصص *</label>
                        <input type="text" class="form-control" id="name" name="name" required maxlength="255">
                        <div class="form-text">أدخل اسم التخصص كاملاً (مثل: ماجستير علوم الحاسوب، دكتوراه الفيزياء)</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSpecialization()">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>



<style>
    .page-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .data-table {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .table {
        color: white;
        margin-bottom: 0;
    }

    .table th {
        background: rgba(255, 255, 255, 0.1);
        border: none;
        color: white;
        font-weight: bold;
        padding: 15px;
        text-align: center;
        border-bottom: 2px solid rgba(255, 255, 255, 0.2);
    }

    .table td {
        border: none;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        vertical-align: middle;
    }

    .table tbody tr:hover {
        background: rgba(255, 255, 255, 0.1);
        transform: scale(1.01);
    }

    .action-btn {
        padding: 8px 15px;
        border-radius: 15px;
        border: none;
        margin: 0 3px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-edit {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
        color: white;
    }

    .btn-edit:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(155, 89, 182, 0.4);
    }

    .btn-delete {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
    }

    .btn-delete:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
    }

    .modal-content {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .modal-header {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
        color: white;
        border-radius: 20px 20px 0 0;
        border-bottom: none;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(155, 89, 182, 0.3);
        padding: 12px 15px;
        transition: all 0.3s ease;
    }

    .form-control:focus, .form-select:focus {
        border-color: #9b59b6;
        box-shadow: 0 0 0 0.2rem rgba(155, 89, 182, 0.25);
    }

    .specialization-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
    }

    .specialization-card:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .specialization-name {
        color: white;
        font-size: 1.2rem;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .specialization-stats {
        display: flex;
        gap: 20px;
        margin-top: 10px;
    }

    .stat-item {
        background: rgba(155, 89, 182, 0.3);
        padding: 8px 15px;
        border-radius: 10px;
        color: white;
        font-size: 0.9rem;
    }
</style>




<script>
let specializations = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSpecializations();
});

// تحميل قائمة التخصصات من قاعدة البيانات
async function loadSpecializations() {
    try {
        console.log('🔄 بدء تحميل التخصصات...');

        // عرض رسالة التحميل
        const tbody = document.getElementById('specializationsTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                        <p class="mt-3 text-muted">جاري تحميل بيانات التخصصات...</p>
                    </td>
                </tr>
            `;
        }

        const response = await fetch('/api/specializations');
        console.log('📡 استجابة التخصصات:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 التخصصات المستلمة:', data);
        console.log('📊 نوع البيانات:', typeof data);
        console.log('📊 هل هي مصفوفة؟', Array.isArray(data));

        if (Array.isArray(data)) {
            specializations = data;
            console.log('✅ تم تحميل', data.length, 'تخصص');
            displaySpecializations(data);
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showAlert('خطأ في تحميل بيانات التخصصات: ' + (data.error || 'بيانات غير صحيحة'), 'error');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل التخصصات:', error);
        showAlert('حدث خطأ أثناء تحميل بيانات التخصصات: ' + error.message, 'error');

        // عرض رسالة خطأ في الجدول
        const tbody = document.getElementById('specializationsTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center py-4">
                        <div class="text-danger">
                            <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                            <h5>خطأ في تحميل البيانات</h5>
                            <p>${error.message}</p>
                            <button class="btn btn-primary" onclick="loadSpecializations()">
                                <i class="fas fa-sync-alt"></i>
                                إعادة المحاولة
                            </button>
                        </div>
                    </td>
                </tr>
            `;
        }
    }
}



// عرض التخصصات في الجدول
function displaySpecializations(data) {
    console.log('🎨 عرض التخصصات:', data.length, 'تخصص');
    const tbody = document.getElementById('specializationsTableBody');

    if (!tbody) {
        console.error('❌ عنصر الجدول غير موجود!');
        return;
    }

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-tags fa-3x mb-3"></i>
                        <h5>لا توجد تخصصات مسجلة حالياً</h5>
                        <p>يمكنك إضافة أول تخصص باستخدام الزر أعلاه</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((specialization, index) => {
        const row = document.createElement('tr');
        const createdDate = specialization.created_date ?
            new Date(specialization.created_date).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) : '-';

        row.innerHTML = `
            <td class="text-center fw-bold">${index + 1}</td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-tag text-primary me-2"></i>
                    <span class="fw-semibold">${specialization.specialization_name || specialization.name}</span>
                </div>
            </td>
            <td>
                <small class="text-muted">
                    <i class="fas fa-calendar-alt me-1"></i>
                    ${createdDate}
                </small>
            </td>
            <td class="text-center">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editSpecialization(${specialization.specialization_id})" title="تعديل التخصص">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteSpecialization(${specialization.specialization_id}, '${(specialization.specialization_name || specialization.name).replace(/'/g, "\\'")}'))" title="حذف التخصص">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    console.log('✅ تم عرض', data.length, 'تخصص بنجاح');
}

// إضافة أو تحديث تخصص
function saveSpecialization() {
    const form = document.getElementById('specializationForm');
    const specializationId = document.getElementById('specializationId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();

    if (!name) {
        showAlert('اسم التخصص مطلوب', 'error');
        return;
    }

    const data = {
        name: name
    };

    const url = specializationId ? `/api/specializations/${specializationId}` : '/api/specializations';
    const method = specializationId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addSpecializationModal')).hide();
            form.reset();
            document.getElementById('specializationId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة تخصص جديد';
            loadSpecializations();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ التخصص', 'error');
    });
}

// تعديل تخصص
function editSpecialization(specializationId) {
    const specialization = specializations.find(s => s.specialization_id === specializationId);
    if (!specialization) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('specializationId').value = specialization.specialization_id;
    document.getElementById('name').value = specialization.name;
    document.getElementById('modalTitle').textContent = 'تعديل بيانات التخصص';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addSpecializationModal')).show();
}

// حذف تخصص
function deleteSpecialization(specializationId, specializationName) {
    if (!confirm(`هل أنت متأكد من حذف التخصص "${specializationName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/specializations/${specializationId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadSpecializations();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف التخصص', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterSpecializations();
});

function filterSpecializations() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    const filteredData = specializations.filter(specialization => {
        return specialization.name.toLowerCase().includes(searchTerm);
    });

    displaySpecializations(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    displaySpecializations(specializations);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addSpecializationModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('specializationForm').reset();
            document.getElementById('specializationId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة تخصص جديد';
        });
    }
});
</script>
{% endblock %}
