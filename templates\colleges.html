{% extends "base.html" %}

{% block title %}إدارة الكليات - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-building"></i>
        إدارة الكليات
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات الكليات</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCollegeModal">
        <i class="fas fa-plus"></i>
        إضافة كلية جديدة
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الكليات...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="universityFilter">
                    <option value="">جميع الجامعات</option>
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Colleges Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="collegesTable">
                <thead>
                    <tr>
                        <th>الشعار</th>
                        <th>الاسم بالعربية</th>
                        <th>الاسم بالإنجليزية</th>
                        <th>الجامعة</th>
                        <th>العنوان</th>
                        <th>تاريخ الإنشاء</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="collegesTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add College Modal -->
<div class="modal fade" id="addCollegeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة كلية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addCollegeForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="universityId" class="form-label">الجامعة *</label>
                                <select class="form-select" id="universityId" name="university_id" required>
                                    <option value="">اختر الجامعة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nameAr" class="form-label">الاسم بالعربية *</label>
                                <input type="text" class="form-control" id="nameAr" name="name_ar" maxlength="255" required>
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nameEn" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" class="form-control" id="nameEn" name="name_en" maxlength="255">
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="logo" class="form-label">شعار الكلية</label>
                                <input type="file" class="form-control" id="logo" name="logo" accept="image/*">
                                <div class="form-text">يُحفظ في قاعدة البيانات</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="address" class="form-label">العنوان</label>
                        <textarea class="form-control" id="address" name="address" rows="3" maxlength="500"></textarea>
                        <div class="form-text">الحد الأقصى 500 حرف</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="addCollege()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit College Modal -->
<div class="modal fade" id="editCollegeModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل بيانات الكلية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editCollegeForm">
                    <input type="hidden" id="editCollegeId" name="college_id">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editUniversityId" class="form-label">الجامعة *</label>
                                <select class="form-select" id="editUniversityId" name="university_id" required>
                                    <option value="">اختر الجامعة</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editNameAr" class="form-label">الاسم بالعربية *</label>
                                <input type="text" class="form-control" id="editNameAr" name="name_ar" maxlength="255" required>
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editNameEn" class="form-label">الاسم بالإنجليزية</label>
                                <input type="text" class="form-control" id="editNameEn" name="name_en" maxlength="255">
                                <div class="form-text">الحد الأقصى 255 حرف</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="editLogo" class="form-label">شعار الكلية</label>
                                <div id="currentCollegeLogo" class="mb-2" style="display: none;">
                                    <label class="form-label">الشعار الحالي:</label>
                                    <div>
                                        <img id="currentCollegeLogoImg" src="" alt="الشعار الحالي" style="width: 80px; height: 80px; object-fit: cover; border-radius: 5px; border: 1px solid #ddd;">
                                    </div>
                                </div>
                                <input type="file" class="form-control" id="editLogo" name="logo" accept="image/*">
                                <div class="form-text">اتركه فارغاً إذا كنت لا تريد تغيير الشعار</div>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="editAddress" class="form-label">العنوان</label>
                        <textarea class="form-control" id="editAddress" name="address" rows="3" maxlength="500"></textarea>
                        <div class="form-text">الحد الأقصى 500 حرف</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="updateCollege()">حفظ التغييرات</button>
            </div>
        </div>
    </div>
</div>

<script>
let colleges = [];
let universities = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadUniversities();
    loadColleges();
});

// تحميل قائمة الجامعات
function loadUniversities() {
    fetch('/api/universities/list')
        .then(response => response.json())
        .then(data => {
            universities = data;
            populateUniversitySelects();
            populateUniversityFilter();
        })
        .catch(error => {
            console.error('خطأ في تحميل الجامعات:', error);
        });
}

// ملء قوائم الجامعات
function populateUniversitySelects() {
    const selects = ['universityId', 'editUniversityId'];
    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        select.innerHTML = '<option value="">اختر الجامعة</option>';
        universities.forEach(university => {
            const option = document.createElement('option');
            option.value = university.university_id;
            option.textContent = university.name_ar;
            select.appendChild(option);
        });
    });
}

// ملء فلتر الجامعات
function populateUniversityFilter() {
    const filter = document.getElementById('universityFilter');
    filter.innerHTML = '<option value="">جميع الجامعات</option>';
    universities.forEach(university => {
        const option = document.createElement('option');
        option.value = university.university_id;
        option.textContent = university.name_ar;
        filter.appendChild(option);
    });
}

// تحميل قائمة الكليات
function loadColleges() {
    fetch('/api/colleges')
        .then(response => response.json())
        .then(data => {
            colleges = data;
            displayColleges(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showAlert('خطأ في تحميل البيانات', 'error');
        });
}

// عرض الكليات في الجدول
function displayColleges(data) {
    const tbody = document.getElementById('collegesTableBody');
    tbody.innerHTML = '';

    data.forEach(college => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                ${college.has_logo ?
                    `<img src="/api/colleges/${college.college_id}/logo" alt="شعار الكلية" style="width: 40px; height: 40px; object-fit: cover; border-radius: 5px;" onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                     <i class="fas fa-building fa-2x text-muted" style="display: none;"></i>` :
                    '<i class="fas fa-building fa-2x text-muted"></i>'
                }
            </td>
            <td>${college.name_ar}</td>
            <td>${college.name_en || '-'}</td>
            <td>${college.university_name || '-'}</td>
            <td>${college.address || '-'}</td>
            <td>${college.created_date || '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editCollege(${college.college_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteCollege(${college.college_id}, '${college.name_ar}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// إضافة كلية جديدة
function addCollege() {
    const form = document.getElementById('addCollegeForm');

    // التحقق من صحة البيانات
    const nameAr = form.querySelector('#nameAr').value.trim();
    const universityId = form.querySelector('#universityId').value;

    if (!nameAr) {
        showAlert('اسم الكلية بالعربية مطلوب', 'error');
        return;
    }

    if (!universityId) {
        showAlert('يجب اختيار الجامعة', 'error');
        return;
    }

    const formData = new FormData(form);

    console.log('إرسال البيانات:', {
        name_ar: formData.get('name_ar'),
        name_en: formData.get('name_en'),
        address: formData.get('address'),
        university_id: formData.get('university_id'),
        logo: formData.get('logo') ? formData.get('logo').name : 'لا يوجد'
    });

    fetch('/api/colleges', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('استجابة الخادم:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('بيانات الاستجابة:', data);
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addCollegeModal')).hide();
            form.reset();
            loadColleges();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في إضافة الكلية', 'error');
    });
}

// تعديل كلية
function editCollege(collegeId) {
    const college = colleges.find(c => c.college_id === collegeId);
    if (!college) return;

    document.getElementById('editCollegeId').value = college.college_id;
    document.getElementById('editUniversityId').value = college.university_id;
    document.getElementById('editNameAr').value = college.name_ar;
    document.getElementById('editNameEn').value = college.name_en || '';
    document.getElementById('editAddress').value = college.address || '';

    // عرض الصورة الحالية إذا كانت موجودة
    const currentLogoDiv = document.getElementById('currentCollegeLogo');
    const currentLogoImg = document.getElementById('currentCollegeLogoImg');

    if (college.has_logo) {
        currentLogoImg.src = `/api/colleges/${college.college_id}/logo`;
        currentLogoDiv.style.display = 'block';
    } else {
        currentLogoDiv.style.display = 'none';
    }

    new bootstrap.Modal(document.getElementById('editCollegeModal')).show();
}

// تحديث بيانات الكلية
function updateCollege() {
    const form = document.getElementById('editCollegeForm');
    const formData = new FormData(form);
    const collegeId = document.getElementById('editCollegeId').value;

    fetch(`/api/colleges/${collegeId}`, {
        method: 'PUT',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('editCollegeModal')).hide();
            loadColleges();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في تحديث الكلية', 'error');
    });
}

// حذف كلية
function deleteCollege(collegeId, collegeName) {
    if (!confirm(`هل أنت متأكد من حذف الكلية "${collegeName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/colleges/${collegeId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadColleges();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف الكلية', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterColleges();
});

document.getElementById('universityFilter').addEventListener('change', function() {
    filterColleges();
});

function filterColleges() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const universityFilter = document.getElementById('universityFilter').value;

    const filteredData = colleges.filter(college => {
        const matchesSearch = college.name_ar.toLowerCase().includes(searchTerm) ||
                            (college.name_en && college.name_en.toLowerCase().includes(searchTerm)) ||
                            (college.address && college.address.toLowerCase().includes(searchTerm)) ||
                            (college.university_name && college.university_name.toLowerCase().includes(searchTerm));

        const matchesUniversity = !universityFilter || college.university_id == universityFilter;

        return matchesSearch && matchesUniversity;
    });

    displayColleges(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('universityFilter').value = '';
    displayColleges(colleges);
}

// تصدير البيانات
function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

// طباعة البيانات
function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}