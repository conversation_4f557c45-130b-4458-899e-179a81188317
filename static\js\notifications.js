/**
 * نظام الإشعارات الموحد لنظام إدارة الدراسات العليا
 * Global Notification System for PGSMS
 * 
 * يوفر إشعارات جميلة ومتسقة عبر جميع صفحات النظام
 * Provides beautiful and consistent notifications across all system pages
 */

// Global Notification System
function showAlert(message, type = 'success', title = null) {
    const types = {
        'success': {
            modal: 'globalSuccessModal',
            titleElement: 'globalSuccessTitle',
            messageElement: 'globalSuccessMessage',
            defaultTitle: 'تم بنجاح!'
        },
        'error': {
            modal: 'globalErrorModal',
            titleElement: 'globalErrorTitle',
            messageElement: 'globalErrorMessage',
            defaultTitle: 'حدث خطأ!'
        },
        'warning': {
            modal: 'globalWarningModal',
            titleElement: 'globalWarningTitle',
            messageElement: 'globalWarningMessage',
            defaultTitle: 'تحذير!'
        },
        'info': {
            modal: 'globalInfoModal',
            titleElement: 'globalInfoTitle',
            messageElement: 'globalInfoMessage',
            defaultTitle: 'معلومة!'
        }
    };

    const config = types[type] || types['info'];
    
    // تحديث النص
    document.getElementById(config.titleElement).textContent = title || config.defaultTitle;
    document.getElementById(config.messageElement).textContent = message;
    
    // إظهار النافذة
    const modal = new bootstrap.Modal(document.getElementById(config.modal));
    modal.show();
    
    // إغلاق تلقائي للإشعارات الناجحة والمعلوماتية
    if (type === 'success' || type === 'info') {
        setTimeout(() => {
            modal.hide();
        }, 3000);
    }
}

// دوال مخصصة لكل نوع
function showSuccessModal(message, title = 'تم الحفظ بنجاح!') {
    showAlert(message, 'success', title);
}

function showErrorModal(message, title = 'حدث خطأ!') {
    showAlert(message, 'error', title);
}

function showWarningModal(message, title = 'تحذير!') {
    showAlert(message, 'warning', title);
}

function showInfoModal(message, title = 'معلومة!') {
    showAlert(message, 'info', title);
}

// شريط التقدم العام
function showProgress(percentage = 0) {
    const progressBar = document.getElementById('globalProgressBar');
    if (progressBar) {
        progressBar.style.width = percentage + '%';
        progressBar.classList.add('show');
    }
}

function hideProgress() {
    const progressBar = document.getElementById('globalProgressBar');
    if (progressBar) {
        progressBar.classList.remove('show');
        setTimeout(() => {
            progressBar.style.width = '0%';
        }, 300);
    }
}

// دوال للعمليات الشائعة
function showSaveSuccess(message = 'تم حفظ البيانات بنجاح') {
    showSuccessModal(message, 'تم الحفظ بنجاح!');
}

function showUpdateSuccess(message = 'تم تحديث البيانات بنجاح') {
    showSuccessModal(message, 'تم التحديث بنجاح!');
}

function showDeleteSuccess(message = 'تم حذف البيانات بنجاح') {
    showSuccessModal(message, 'تم الحذف بنجاح!');
}

function showSaveError(message = 'فشل في حفظ البيانات') {
    showErrorModal(message, 'خطأ في الحفظ!');
}

function showUpdateError(message = 'فشل في تحديث البيانات') {
    showErrorModal(message, 'خطأ في التحديث!');
}

function showDeleteError(message = 'فشل في حذف البيانات') {
    showErrorModal(message, 'خطأ في الحذف!');
}

function showLoadingError(message = 'فشل في تحميل البيانات') {
    showErrorModal(message, 'خطأ في التحميل!');
}

function showConnectionError(message = 'فشل في الاتصال بالخادم') {
    showErrorModal(message, 'خطأ في الاتصال!');
}

function showValidationError(message = 'يرجى التحقق من البيانات المدخلة') {
    showWarningModal(message, 'بيانات غير صحيحة!');
}

function showRequiredFieldError(message = 'يرجى ملء جميع الحقول المطلوبة') {
    showWarningModal(message, 'حقول مطلوبة!');
}

// دوال للعمليات المتقدمة
function showOperationInProgress(message = 'جاري تنفيذ العملية...') {
    showInfoModal(message, 'جاري التنفيذ...');
}

function showBackupSuccess(message = 'تم إنشاء النسخة الاحتياطية بنجاح') {
    showSuccessModal(message, 'نسخ احتياطي ناجح!');
}

function showRestoreSuccess(message = 'تم استعادة النسخة الاحتياطية بنجاح') {
    showSuccessModal(message, 'استعادة ناجحة!');
}

function showExportSuccess(message = 'تم تصدير البيانات بنجاح') {
    showSuccessModal(message, 'تصدير ناجح!');
}

function showImportSuccess(message = 'تم استيراد البيانات بنجاح') {
    showSuccessModal(message, 'استيراد ناجح!');
}

// دوال للتحقق من صحة البيانات
function validateRequired(value, fieldName) {
    if (!value || value.trim() === '') {
        showRequiredFieldError(`حقل ${fieldName} مطلوب`);
        return false;
    }
    return true;
}

function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
        showValidationError('يرجى إدخال بريد إلكتروني صحيح');
        return false;
    }
    return true;
}

function validatePhone(phone) {
    const phoneRegex = /^[0-9+\-\s()]+$/;
    if (!phoneRegex.test(phone)) {
        showValidationError('يرجى إدخال رقم هاتف صحيح');
        return false;
    }
    return true;
}

// دوال مساعدة للتعامل مع الاستجابات
function handleApiResponse(response, successMessage = 'تمت العملية بنجاح') {
    if (response.success) {
        showSaveSuccess(response.message || successMessage);
        return true;
    } else {
        showSaveError(response.error || 'حدث خطأ غير متوقع');
        return false;
    }
}

function handleApiError(error, defaultMessage = 'حدث خطأ في الاتصال بالخادم') {
    console.error('API Error:', error);
    showConnectionError(error.message || defaultMessage);
}

// دالة لإظهار تأكيد العملية
function showConfirmation(message, title = 'تأكيد العملية', onConfirm = null) {
    if (confirm(`${title}\n\n${message}`)) {
        if (onConfirm && typeof onConfirm === 'function') {
            onConfirm();
        }
        return true;
    }
    return false;
}

// دالة لإظهار تأكيد الحذف
function confirmDelete(itemName, onConfirm = null) {
    const message = `هل أنت متأكد من حذف "${itemName}"؟\n\nهذه العملية لا يمكن التراجع عنها.`;
    return showConfirmation(message, 'تأكيد الحذف', onConfirm);
}

// تصدير الدوال للاستخدام العام
window.showAlert = showAlert;
window.showSuccessModal = showSuccessModal;
window.showErrorModal = showErrorModal;
window.showWarningModal = showWarningModal;
window.showInfoModal = showInfoModal;
window.showProgress = showProgress;
window.hideProgress = hideProgress;
window.showSaveSuccess = showSaveSuccess;
window.showUpdateSuccess = showUpdateSuccess;
window.showDeleteSuccess = showDeleteSuccess;
window.showSaveError = showSaveError;
window.showUpdateError = showUpdateError;
window.showDeleteError = showDeleteError;
window.showLoadingError = showLoadingError;
window.showConnectionError = showConnectionError;
window.showValidationError = showValidationError;
window.showRequiredFieldError = showRequiredFieldError;
window.showOperationInProgress = showOperationInProgress;
window.showBackupSuccess = showBackupSuccess;
window.showRestoreSuccess = showRestoreSuccess;
window.showExportSuccess = showExportSuccess;
window.showImportSuccess = showImportSuccess;
window.validateRequired = validateRequired;
window.validateEmail = validateEmail;
window.validatePhone = validatePhone;
window.handleApiResponse = handleApiResponse;
window.handleApiError = handleApiError;
window.showConfirmation = showConfirmation;
window.confirmDelete = confirmDelete;
