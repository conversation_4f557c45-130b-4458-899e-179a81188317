{% extends "base.html" %}

{% block title %}الإعدادات - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-cog"></i>
        إعدادات النظام
    </h1>
    <p class="page-subtitle">إدارة إعدادات النظام وقاعدة البيانات</p>
</div>



<style>
    .connection-status {
        padding: 1rem;
        border-radius: 8px;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .status-connected {
        background-color: #d4edda;
        color: #155724;
        border: 1px solid #c3e6cb;
    }

    .status-disconnected {
        background-color: #f8d7da;
        color: #721c24;
        border: 1px solid #f5c6cb;
    }

    .status-testing {
        background-color: #fff3cd;
        color: #856404;
        border: 1px solid #ffeaa7;
    }
</style>

<!-- Database Connection Settings -->
<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title">
                    <i class="fas fa-database"></i>
                    إعدادات الاتصال بقاعدة البيانات
                </h5>

                <!-- Connection Status -->
                <div id="connectionStatus" class="connection-status status-testing">
                    <i class="fas fa-spinner fa-spin"></i>
                    جاري فحص الاتصال...
                </div>

                <form id="databaseForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="server" class="form-label">
                                    <i class="fas fa-server"></i>
                                    اسم الخادم
                                </label>
                                <input type="text" class="form-control" id="server" name="server"
                                       value="HUSSEIN\SQLEXPRESS" required>
                                <div class="form-text">مثال: HUSSEIN\SQLEXPRESS أو localhost</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="database" class="form-label">
                                    <i class="fas fa-database"></i>
                                    اسم قاعدة البيانات
                                </label>
                                <input type="text" class="form-control" id="database" name="database"
                                       value="PGSMS" required>
                                <div class="form-text">اسم قاعدة البيانات المراد الاتصال بها</div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="driver" class="form-label">
                                    <i class="fas fa-plug"></i>
                                    برنامج التشغيل
                                </label>
                                <select class="form-select" id="driver" name="driver" required>
                                    <option value="{ODBC Driver 17 for SQL Server}" selected>ODBC Driver 17 for SQL Server</option>
                                    <option value="{ODBC Driver 13 for SQL Server}">ODBC Driver 13 for SQL Server</option>
                                    <option value="{SQL Server}">SQL Server</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="authentication" class="form-label">
                                    <i class="fas fa-key"></i>
                                    نوع المصادقة
                                </label>
                                <select class="form-select" id="authentication" name="authentication" required>
                                    <option value="windows" selected>Windows Authentication</option>
                                    <option value="sql">SQL Server Authentication</option>
                                </select>
                            </div>
                        </div>
                    </div>

                            <!-- SQL Authentication Fields (hidden by default) -->
                            <div id="sqlAuthFields" style="display: none;">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">
                                                <i class="fas fa-user"></i>
                                                اسم المستخدم
                                            </label>
                                            <input type="text" class="form-control" id="username" name="username">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="password" class="form-label">
                                                <i class="fas fa-lock"></i>
                                                كلمة المرور
                                            </label>
                                            <input type="password" class="form-control" id="password" name="password">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex gap-2">
                                <button type="button" class="btn btn-primary" onclick="testConnection()">
                                    <i class="fas fa-plug"></i>
                                    اختبار الاتصال
                                </button>
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-save"></i>
                                    حفظ الإعدادات
                                </button>
                                <button type="button" class="btn btn-warning" onclick="createDatabase()">
                                    <i class="fas fa-plus"></i>
                                    إنشاء قاعدة البيانات
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-info-circle"></i>
                            معلومات النظام
                        </h5>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <tbody>
                                    <tr>
                                        <td><strong>إصدار النظام:</strong></td>
                                        <td><span class="text-muted">1.0.0</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>حالة قاعدة البيانات:</strong></td>
                                        <td><span class="badge bg-secondary" id="dbStatus">غير معروف</span></td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر تحديث:</strong></td>
                                        <td id="lastUpdate">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>عدد الجداول:</strong></td>
                                        <td id="tableCount">-</td>
                                    </tr>
                                    <tr>
                                        <td><strong>حجم قاعدة البيانات:</strong></td>
                                        <td id="dbSize">-</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-bolt"></i>
                            إجراءات سريعة
                        </h5>
                        <div class="d-grid gap-2">
                            <button class="btn btn-outline-primary" onclick="refreshSystemInfo()">
                                <i class="fas fa-sync-alt"></i>
                                تحديث معلومات النظام
                            </button>
                            <button class="btn btn-outline-warning" onclick="optimizeDatabase()">
                                <i class="fas fa-tools"></i>
                                تحسين قاعدة البيانات
                            </button>
                            <button class="btn btn-outline-info" onclick="exportSettings()">
                                <i class="fas fa-download"></i>
                                تصدير الإعدادات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Check connection status on page load
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة الإعدادات...');

    try {
        checkConnectionStatus();
        loadSystemInfo();

        // Toggle SQL authentication fields
        const authSelect = document.getElementById('authentication');
        if (authSelect) {
            authSelect.addEventListener('change', function() {
                const sqlFields = document.getElementById('sqlAuthFields');
                if (sqlFields) {
                    if (this.value === 'sql') {
                        sqlFields.style.display = 'block';
                    } else {
                        sqlFields.style.display = 'none';
                    }
                }
            });
        }

        console.log('✅ تم تحميل صفحة الإعدادات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل صفحة الإعدادات:', error);
    }
});

function checkConnectionStatus() {
    const statusDiv = document.getElementById('connectionStatus');
    const dbStatus = document.getElementById('dbStatus');

    // إظهار حالة التحميل
    statusDiv.className = 'connection-status status-testing';
    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري فحص الاتصال...';

    fetch('/api/settings/test-connection')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                statusDiv.className = 'connection-status status-connected';
                statusDiv.innerHTML = '<i class="fas fa-check-circle"></i> الاتصال بقاعدة البيانات متاح';
                if (dbStatus) {
                    dbStatus.className = 'badge bg-success';
                    dbStatus.textContent = 'متصل';
                }
            } else {
                statusDiv.className = 'connection-status status-disconnected';
                statusDiv.innerHTML = '<i class="fas fa-times-circle"></i> فشل الاتصال بقاعدة البيانات: ' + (data.error || 'خطأ غير معروف');
                if (dbStatus) {
                    dbStatus.className = 'badge bg-danger';
                    dbStatus.textContent = 'غير متصل';
                }
            }
        })
        .catch(error => {
            console.error('خطأ في فحص الاتصال:', error);
            statusDiv.className = 'connection-status status-disconnected';
            statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في فحص الاتصال: ' + error.message;
            if (dbStatus) {
                dbStatus.className = 'badge bg-danger';
                dbStatus.textContent = 'خطأ';
            }
        });
}

function testConnection() {
    const formData = new FormData(document.getElementById('databaseForm'));
    const statusDiv = document.getElementById('connectionStatus');

    statusDiv.className = 'connection-status status-testing';
    statusDiv.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري اختبار الاتصال...';

    fetch('/api/settings/test-connection', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            statusDiv.className = 'connection-status status-connected';
            statusDiv.innerHTML = '<i class="fas fa-check-circle"></i> تم الاتصال بنجاح!';
            showSuccessModal('تم اختبار الاتصال بقاعدة البيانات بنجاح', 'اتصال ناجح!');
        } else {
            statusDiv.className = 'connection-status status-disconnected';
            statusDiv.innerHTML = '<i class="fas fa-times-circle"></i> فشل الاتصال: ' + (data.error || 'خطأ غير معروف');
            showErrorModal('فشل في الاتصال: ' + (data.error || 'خطأ غير معروف'), 'فشل الاتصال!');
        }
    })
    .catch(error => {
        console.error('خطأ في اختبار الاتصال:', error);
        statusDiv.className = 'connection-status status-disconnected';
        statusDiv.innerHTML = '<i class="fas fa-exclamation-triangle"></i> خطأ في اختبار الاتصال: ' + error.message;
        showErrorModal('خطأ في اختبار الاتصال: ' + error.message, 'خطأ في الاتصال!');
    });
}

function createDatabase() {
    if (!confirm('هل أنت متأكد من إنشاء قاعدة البيانات؟ سيتم إنشاء جميع الجداول المطلوبة.')) {
        return;
    }
    
    const formData = new FormData(document.getElementById('databaseForm'));
    
    fetch('/api/settings/create-database', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessModal('تم إنشاء قاعدة البيانات وجميع الجداول بنجاح', 'إنشاء ناجح!');
            checkConnectionStatus();
            loadSystemInfo();
        } else {
            showErrorModal('فشل في إنشاء قاعدة البيانات: ' + data.error, 'فشل الإنشاء!');
        }
    })
    .catch(error => {
        showErrorModal('خطأ في إنشاء قاعدة البيانات', 'خطأ في الإنشاء!');
    });
}

function loadSystemInfo() {
    fetch('/api/settings/system-info')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('lastUpdate').textContent = data.last_update || '-';
                document.getElementById('tableCount').textContent = data.table_count || '-';
                document.getElementById('dbSize').textContent = data.db_size || '-';
            }
        })
        .catch(error => {
            console.error('Error loading system info:', error);
        });
}

function refreshSystemInfo() {
    showInfoModal('جاري تحديث معلومات النظام...', 'تحديث المعلومات');
    loadSystemInfo();
    checkConnectionStatus();
    setTimeout(() => {
        showSuccessModal('تم تحديث معلومات النظام بنجاح', 'تحديث ناجح!');
    }, 1000);
}

function optimizeDatabase() {
    if (!confirm('هل أنت متأكد من تحسين قاعدة البيانات؟ قد تستغرق هذه العملية بعض الوقت.')) {
        return;
    }
    
    fetch('/api/settings/optimize-database', {
        method: 'POST'
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessModal('تم تحسين قاعدة البيانات وتحديث الفهارس بنجاح', 'تحسين ناجح!');
        } else {
            showErrorModal('فشل في تحسين قاعدة البيانات: ' + data.error, 'فشل التحسين!');
        }
    })
    .catch(error => {
        showErrorModal('خطأ في تحسين قاعدة البيانات', 'خطأ في التحسين!');
    });
}

function exportSettings() {
    fetch('/api/settings/export')
        .then(response => response.blob())
        .then(blob => {
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'settings_' + new Date().toISOString().slice(0, 10) + '.json';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            showSuccessModal('تم تصدير إعدادات النظام بنجاح', 'تصدير ناجح!');
        })
        .catch(error => {
            showErrorModal('خطأ في تصدير الإعدادات', 'فشل التصدير!');
        });
}

// Handle form submission
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('databaseForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        fetch('/api/settings/save', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSaveSuccess('تم حفظ إعدادات قاعدة البيانات بنجاح');
                checkConnectionStatus();
            } else {
                showSaveError('فشل في حفظ الإعدادات: ' + data.error);
            }
        })
        .catch(error => {
            showSaveError('خطأ في حفظ الإعدادات');
        });
    });
});


</script>
{% endblock %}
