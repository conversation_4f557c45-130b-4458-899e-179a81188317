<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الدراسات العليا</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Neo Sans Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap" rel="stylesheet">
    
    <style>
        * {
            font-family: 'Noto Sans Arabic', 'Neo Sans Arabic Bold', sans-serif !important;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #4facfe 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            z-index: 0;
        }

        .login-container {
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(10px);
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            width: 100%;
            max-width: 380px;
            animation: slideUp 0.8s ease;
            position: relative;
            z-index: 1;
            border: none;
        }

        @keyframes slideUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
        }

        .system-title-ar {
            color: #1f2937;
            font-weight: 700;
            margin-bottom: 8px;
            font-size: 1.8rem;
            line-height: 1.3;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .system-title-en {
            color: #6b7280;
            font-weight: 500;
            margin-bottom: 0;
            font-size: 1.1rem;
            line-height: 1.4;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            letter-spacing: 0.5px;
        }





        .form-group {
            margin-bottom: 18px;
            position: relative;
        }

        .form-group:last-of-type {
            margin-bottom: 12px;
        }

        .form-label {
            font-weight: 500;
            color: #6b7280;
            font-size: 0.9rem;
            margin-bottom: 0.5rem;
            display: block;
        }

        .form-control, .form-select {
            border-radius: 8px;
            border: 1px solid #d1d5db;
            padding: 12px 16px;
            transition: all 0.2s ease;
            font-size: 1rem;
            background: #f9fafb;
            color: #374151;
            box-shadow: none;
            width: 100%;
        }

        .form-control::placeholder {
            color: #9ca3af;
        }

        .form-control:focus, .form-select:focus {
            border-color: #3b82f6;
            background: #ffffff;
            outline: none;
            box-shadow: none;
        }

        /* تصميم حقل تذكرني */
        .form-check {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin: 0;
            padding: 0;
            width: 100%;
        }

        .form-check-input {
            margin: 0;
            width: 16px;
            height: 16px;
            margin-right: 0;
        }

        .form-check-label {
            margin: 0;
            color: #374151;
            font-weight: 500;
            cursor: pointer;
            font-size: 0.9rem;
            flex: 1;
            text-align: right;
            margin-left: 8px;
        }

        .input-group {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
            margin-bottom: 1.5rem;
        }

        .input-group-text {
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            color: #6b7280;
            border-radius: 12px 0 0 12px;
            padding: 14px 16px;
            transition: all 0.3s ease;
            border-right: none;
        }

        .input-group:focus-within .input-group-text {
            background: #f0f4ff;
            border-color: #6366f1;
            color: #6366f1;
        }

        .input-group-text i {
            font-size: 1.1rem;
            transition: all 0.3s ease;
        }

        .input-group .form-control, .input-group .form-select {
            border-radius: 0 12px 12px 0;
            border-left: none;
        }

        .input-group .form-control:focus, .input-group .form-select:focus {
            border-left: 2px solid #6366f1;
        }

        .btn-login {
            background: #3b82f6;
            color: white;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            padding: 12px 24px;
            transition: all 0.2s ease;
            font-size: 1rem;
            width: 100%;
            margin-top: 15px;
            box-shadow: none;
            outline: none !important;
        }

        .btn-login:hover {
            background: #2563eb;
            transform: none;
            box-shadow: none;
        }

        .btn-login:active {
            transform: none;
            background: #1d4ed8;
        }

        .btn-login:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .btn-login i {
            margin-left: 8px;
            transition: all 0.3s ease;
        }

        .alert {
            border-radius: 8px;
            border: 1px solid #fecaca;
            background: #fef2f2;
            color: #dc2626;
            padding: 12px 16px;
            margin-bottom: 20px;
            font-size: 0.9rem;
        }

        .alert-success {
            background: #f0fdf4;
            border-color: #bbf7d0;
            color: #16a34a;
        }



        /* إزالة جميع المربعات والحدود نهائياً */
        *, *:before, *:after {
            outline: none !important;
            -webkit-tap-highlight-color: transparent !important;
            box-shadow: none !important;
        }

        /* إزالة outline من جميع العناصر */
        input, select, textarea, button, a, div, span, label {
            outline: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }

        /* إزالة focus outline من جميع العناصر */
        input:focus, select:focus, textarea:focus, button:focus, a:focus {
            outline: none !important;
            box-shadow: none !important;
            border: none !important;
        }

        /* إعادة تعريف تأثيرات التركيز بدون مربعات */
        .form-control:focus, .form-select:focus {
            outline: none !important;
            box-shadow: none !important;
            border: 2px solid #6366f1 !important;
            background: #f8fafc !important;
        }

        .btn-login:focus, .btn-login:active {
            outline: none !important;
            box-shadow: none !important;
        }

        .form-check-input:focus, .form-check-input:active {
            outline: none !important;
            box-shadow: none !important;
            border: 2px solid #6366f1 !important;
        }

        /* إزالة مربعات من العناصر التفاعلية */
        .input-group-text:focus, .logo:focus, .system-info:focus,
        .create-account-link:focus, .create-account-link:active {
            outline: none !important;
            box-shadow: none !important;
        }

        /* إزالة جميع مربعات المتصفح */
        input[type="text"], input[type="password"], select, button {
            outline: none !important;
            -webkit-appearance: none !important;
            -moz-appearance: none !important;
            appearance: none !important;
        }

        input[type="text"]:focus, input[type="password"]:focus,
        select:focus, button:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        /* إزالة مربعات من الروابط */
        a, a:focus, a:active, a:visited {
            outline: none !important;
            box-shadow: none !important;
        }

        /* إزالة مربعات من جميع عناصر Bootstrap */
        .form-control, .form-select, .btn, .input-group-text {
            outline: none !important;
            box-shadow: none !important;
        }

        .form-control:focus, .form-select:focus, .btn:focus, .input-group-text:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        /* إزالة مربعات من عناصر HTML5 */
        input[type="checkbox"], input[type="radio"] {
            outline: none !important;
            box-shadow: none !important;
        }

        input[type="checkbox"]:focus, input[type="radio"]:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        /* إزالة مربعات من Firefox */
        input::-moz-focus-inner, button::-moz-focus-inner {
            border: 0 !important;
            outline: none !important;
        }

        /* إزالة مربعات من Safari/Chrome */
        input:focus, select:focus, textarea:focus, button:focus {
            outline: none !important;
            outline-offset: 0 !important;
        }

        /* إزالة مربعات من Edge/IE */
        input::-ms-clear, input::-ms-reveal {
            display: none !important;
        }

        /* قواعد شاملة لإزالة جميع المربعات */
        * {
            outline: none !important;
            outline-width: 0 !important;
            outline-style: none !important;
            outline-color: transparent !important;
        }

        *:focus {
            outline: none !important;
            outline-width: 0 !important;
            outline-style: none !important;
            outline-color: transparent !important;
        }

        /* تنسيق خيار تذكرني - المربع داخل الحقل */
        .form-check {
            position: relative;
            margin-bottom: 0;
        }

        .form-check-input {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            width: 16px;
            height: 16px;
            border: 2px solid #d1d5db;
            border-radius: 4px;
            background: #ffffff;
            transition: all 0.3s ease;
            cursor: pointer;
            outline: none !important;
            z-index: 2;
        }

        .form-check-input:checked {
            background: #6366f1;
            border-color: #6366f1;
        }

        .form-check-input:focus {
            outline: none !important;
            box-shadow: none !important;
        }

        .form-check-label {
            color: #374151;
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            display: block;
            padding: 14px 16px 14px 45px;
            margin-bottom: 0;
            background: #f8fafc;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
        }

        .form-check-label:hover {
            background: #f1f5f9;
            border-color: #cbd5e1;
        }

        .form-check-input:checked + .form-check-label {
            background: #f0f4ff;
            border-color: #6366f1;
            color: #1e293b;
        }

        .form-check-label i {
            margin-left: 8px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        /* تصميم قسم إنشاء الحساب */
        .create-account-section {
            text-align: center;
            margin-top: 20px;
            padding-top: 0;
            border-top: none;
        }

        .create-account-link {
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }

        .create-account-link:hover {
            color: #2563eb;
            text-decoration: underline;
        }



        /* تصميم معلومات المطور */
        .developer-footer {
            text-align: center;
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .developer-footer .developer-text {
            color: #64748b;
            font-size: 0.85rem;
            margin-bottom: 8px;
            line-height: 1.5;
            font-weight: 500;
        }

        .developer-footer .developer-text strong {
            color: #374151;
            font-weight: 700;
        }

        .developer-footer .copyright {
            color: #9ca3af;
            font-size: 0.8rem;
            margin-bottom: 0;
            font-weight: 500;
        }




    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="system-title-ar">نظام إدارة الدراسات العليا</h1>
            <h2 class="system-title-en">Postgraduate Studies Management System</h2>
        </div>

        <!-- Flash Messages -->
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} fade show" role="alert">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST">
            <div class="form-group">
                <label for="username" class="form-label">اسم المستخدم</label>
                <select class="form-control" id="username" name="username" required onchange="focusPassword()">
                    <option value="">اختر اسم المستخدم</option>
                    {% for user in users %}
                    <option value="{{ user.username }}">{{ user.full_name }} ({{ user.username }})</option>
                    {% endfor %}
                </select>
            </div>

            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" required
                       placeholder="أدخل كلمة المرور">
            </div>

            <!-- خيار تذكرني -->
            <div class="form-group">
                <div class="form-check">
                    <label class="form-check-label" for="remember_me">تذكرني</label>
                    <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                </div>
            </div>

            <button type="submit" class="btn btn-login">تسجيل الدخول</button>
        </form>

        <!-- رابط إنشاء حساب جديد -->
        <div class="create-account-section">
            <a href="#" class="create-account-link" onclick="showCreateAccountForm()">إنشاء حساب جديد</a>
        </div>

        <!-- معلومات المطور -->
        <div class="developer-footer">
            <p class="developer-text">تم تطوير البرنامج بواسطة <strong>حسين الجنابي</strong> بأستخدام إضافة الذكاء الاصطناعي الاحترافية <strong>(Augment)</strong></p>
            <p class="copyright">جميع الحقوق محفوظة © 2025</p>
        </div>


    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Auto focus on username field
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('username').focus();

            // تحميل بيانات "تذكرني" المحفوظة
            loadRememberedCredentials();
        });

        // وظيفة التركيز التلقائي على حقل كلمة المرور عند اختيار المستخدم
        function focusPassword() {
            console.log('تم استدعاء وظيفة focusPassword');
            try {
                const usernameSelect = document.getElementById('username');
                const passwordField = document.getElementById('password');

                console.log('اسم المستخدم المحدد:', usernameSelect ? usernameSelect.value : 'غير موجود');
                console.log('حقل كلمة المرور:', passwordField ? 'موجود' : 'غير موجود');

                if (usernameSelect && passwordField && usernameSelect.value && usernameSelect.value !== "") {
                    console.log('الانتقال إلى حقل كلمة المرور...');
                    setTimeout(() => {
                        passwordField.focus();
                        passwordField.select(); // تحديد النص الموجود إن وجد
                        console.log('تم التركيز على حقل كلمة المرور');
                    }, 100);
                } else {
                    console.log('لم يتم الانتقال - شروط غير مستوفاة');
                }
            } catch (error) {
                console.error('خطأ في وظيفة focusPassword:', error);
            }
        }

        // حفظ وتحميل بيانات "تذكرني"
        function saveRememberedCredentials() {
            const username = document.getElementById('username').value;
            const rememberMe = document.getElementById('remember_me').checked;

            if (rememberMe && username) {
                localStorage.setItem('remembered_username', username);
                localStorage.setItem('remember_me', 'true');
            } else {
                localStorage.removeItem('remembered_username');
                localStorage.removeItem('remember_me');
            }
        }

        function loadRememberedCredentials() {
            const rememberedUsername = localStorage.getItem('remembered_username');
            const rememberMe = localStorage.getItem('remember_me');

            if (rememberMe === 'true' && rememberedUsername) {
                document.getElementById('username').value = rememberedUsername;
                document.getElementById('remember_me').checked = true;
            }
        }

        // حفظ البيانات عند إرسال النموذج
        document.querySelector('form').addEventListener('submit', function() {
            saveRememberedCredentials();
        });

        // Enter key handling
        document.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                const activeElement = document.activeElement;
                if (activeElement.id === 'username' && activeElement.value) {
                    // إذا كان المستخدم في حقل اسم المستخدم، انتقل إلى كلمة المرور
                    document.getElementById('password').focus();
                } else if (activeElement.id === 'password' && activeElement.value) {
                    // إذا كان في حقل كلمة المرور، أرسل النموذج
                    document.querySelector('form').submit();
                }
            }
        });

        // إظهار نموذج إنشاء الحساب
        function showCreateAccountForm() {
            // إخفاء نموذج تسجيل الدخول
            const loginForm = document.querySelector('form');
            const createAccountSection = document.querySelector('.create-account-section');
            const developerFooter = document.querySelector('.developer-footer');

            if (loginForm) loginForm.style.display = 'none';
            if (createAccountSection) createAccountSection.style.display = 'none';
            if (developerFooter) developerFooter.style.display = 'none';

            // إنشاء نموذج إنشاء الحساب مضغوط
            const newFormHTML = '<div class="login-header" style="margin-bottom: 20px; padding-bottom: 15px;">' +
                '<h1 class="system-title-ar" style="font-size: 1.5rem; margin-bottom: 5px;">إنشاء حساب جديد</h1>' +
                '<h2 class="system-title-en" style="font-size: 1rem;">Create New Account</h2>' +
                '</div>' +
                '<form method="POST" action="/register">' +
                '<div class="form-group" style="margin-bottom: 15px;">' +
                '<label for="new_username" class="form-label">اسم المستخدم</label>' +
                '<input type="text" class="form-control" id="new_username" name="username" required placeholder="أدخل اسم المستخدم">' +
                '</div>' +
                '<div class="form-group" style="margin-bottom: 15px;">' +
                '<label for="full_name" class="form-label">الاسم الكامل</label>' +
                '<input type="text" class="form-control" id="full_name" name="full_name" required placeholder="أدخل الاسم الكامل">' +
                '</div>' +
                '<div class="form-group" style="margin-bottom: 15px;">' +
                '<label for="new_password" class="form-label">كلمة المرور</label>' +
                '<input type="password" class="form-control" id="new_password" name="password" required placeholder="أدخل كلمة المرور">' +
                '</div>' +
                '<button type="submit" class="btn btn-login" style="margin-top: 10px;">إنشاء الحساب</button>' +
                '</form>' +
                '<div class="create-account-section" style="margin-top: 15px;">' +
                '<a href="#" class="create-account-link" onclick="showLoginForm()">العودة لتسجيل الدخول</a>' +
                '</div>';

            // استبدال المحتوى
            const container = document.querySelector('.login-container');
            if (container) {
                container.innerHTML = newFormHTML;

                // التركيز على أول حقل
                setTimeout(() => {
                    const firstInput = document.getElementById('new_username');
                    if (firstInput) firstInput.focus();
                }, 100);
            }

        }

        // إظهار نموذج تسجيل الدخول
        function showLoginForm() {
            // إعادة تحميل الصفحة لاستعادة النموذج الأصلي
            window.location.reload();
        }
    </script>
</body>
</html>
