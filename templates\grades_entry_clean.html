{% extends "base.html" %}

{% block title %}إدخال الدرجات - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<style>


@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateX(0); }
    to { opacity: 0; transform: translateX(100%); }
}

/* Success Modal Styles */
.success-icon-container {
    position: relative;
    display: inline-block;
}

.success-animation {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80px;
    height: 80px;
    border: 3px solid #28a745;
    border-radius: 50%;
    animation: successPulse 1.5s ease-out;
    opacity: 0;
}

@keyframes successPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* Loading Animation */
.spinner-border {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Toast Notification Styles */
.toast-notification {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: none;
    border-radius: 15px;
    box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    animation: toastSlideIn 0.5s ease-out;
}

@keyframes toastSlideIn {
    from {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

.toast-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 15px 15px 0 0;
}

.toast-body {
    padding: 1.5rem;
    font-weight: 500;
}

/* Progress Bar for Save Operation */
.save-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
    background-size: 200% 100%;
    animation: progressMove 2s ease-in-out;
    z-index: 10000;
    opacity: 0;
}

.save-progress.active {
    opacity: 1;
}

@keyframes progressMove {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}


</style>
<!-- Progress Bar -->
<div id="saveProgressBar" class="save-progress"></div>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-graduation-cap"></i>
        إدخال الدرجات
    </h1>
    <p class="page-subtitle">إدارة وتسجيل درجات الطلبة حسب المعايير المحددة</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" onclick="showFilters()">
        <i class="fas fa-filter"></i>
        عرض المعايير
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4" id="filtersCard">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-filter"></i>
            معايير مواد السنة التحضيرية
        </h5>
    </div>
    <div class="card-body">
        <form id="filterForm">
            <div class="row g-3">
                <div class="col-lg-3 col-md-6">
                    <label for="academic_year" class="form-label">
                        <i class="fas fa-calendar-alt text-primary"></i>
                        العام الدراسي
                    </label>
                    <select class="form-select" id="academic_year" name="academic_year" required onchange="loadSemesters(); updateSubjects();">
                        <option value="">اختر العام الدراسي</option>
                        {% for year in academic_years %}
                        <option value="{{ year.year_id }}">{{ year.year_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="semester" class="form-label">
                        <i class="fas fa-clock text-primary"></i>
                        الفصل الدراسي
                    </label>
                    <select class="form-select" id="semester" name="semester" required disabled>
                        <option value="">اختر العام الدراسي أولاً</option>
                    </select>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="academic_program" class="form-label">
                        <i class="fas fa-university text-primary"></i>
                        البرنامج الأكاديمي
                    </label>
                    <select class="form-select" id="academic_program" name="academic_program" required onchange="loadSubjects()">
                        <option value="">اختر البرنامج الأكاديمي</option>
                        {% for program in academic_programs %}
                        <option value="{{ program.program_id }}">{{ program.program_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-3 col-md-6">
                    <label for="subject" class="form-label">
                        <i class="fas fa-book text-primary"></i>
                        اسم المادة
                    </label>
                    <select class="form-select" id="subject" name="subject" required disabled>
                        <option value="">اختر البرنامج الأكاديمي أولاً</option>
                    </select>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-md-8">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                        عرض أسماء الطلبة
                    </button>
                </div>
                <div class="col-md-4">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearFilters()">
                        <i class="fas fa-times"></i>
                        مسح المعايير
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Research Year Subjects Section -->
<div class="card mb-4">
    <div class="card-header bg-info text-white">
        <h5 class="mb-0">
            <i class="fas fa-microscope"></i>
            معايير مواد السنة البحثية
        </h5>
    </div>
    <div class="card-body">
        <form id="researchFilterForm">
            <div class="row g-2">
                <div class="col-md-3">
                    <label for="research_academic_year" class="form-label">
                        <i class="fas fa-calendar-alt"></i>
                        العام الدراسي
                    </label>
                    <select class="form-select form-select-sm" id="research_academic_year" name="research_academic_year" required>
                        <option value="">اختر العام الدراسي</option>
                        {% for year in academic_years %}
                        <option value="{{ year.year_id }}">{{ year.year_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="research_academic_program" class="form-label">
                        <i class="fas fa-graduation-cap"></i>
                        البرنامج الأكاديمي
                    </label>
                    <select class="form-select form-select-sm" id="research_academic_program" name="research_academic_program" required>
                        <option value="">اختر البرنامج الأكاديمي</option>
                        {% for program in academic_programs %}
                        <option value="{{ program.program_id }}">{{ program.program_name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="research_subject" class="form-label">
                        <i class="fas fa-book"></i>
                        اسم المادة
                    </label>
                    <select class="form-select form-select-sm" id="research_subject" name="research_subject" required>
                        <option value="">اختر اسم المادة</option>
                        <option value="السمنار">السمنار</option>
                        <option value="الرسالة">الرسالة</option>
                        <option value="الاطروحة">الاطروحة</option>
                        <option value="مشروع بحث">مشروع بحث</option>
                    </select>
                </div>

                <div class="col-md-3">
                    <label for="research_student" class="form-label">
                        <i class="fas fa-user-graduate"></i>
                        اسم الطالب
                    </label>
                    <select class="form-select form-select-sm" id="research_student" name="research_student" required>
                        <option value="">اختر اسم الطالب</option>
                    </select>
                </div>
            </div>

            <div class="row mt-3">
                <div class="col-md-6">
                    <button type="button" class="btn btn-info w-100" disabled>
                        <i class="fas fa-info-circle"></i>
                        الدرجة تظهر تلقائياً عند اختيار الطالب
                    </button>
                </div>
                <div class="col-md-6">
                    <button type="button" class="btn btn-secondary w-100" onclick="clearResearchFilters()">
                        <i class="fas fa-times"></i>
                        مسح المعايير
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Research Grade Entry Section -->
<div class="card mb-4" id="researchGradeCard" style="display: none;">
    <div class="card-header bg-success text-white">
        <h5 class="mb-0">
            <i class="fas fa-edit"></i>
            إدخال درجة السنة البحثية
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="research_credit_hours" class="form-label">
                        <i class="fas fa-clock text-primary"></i>
                        عدد الوحدات
                    </label>
                    <input type="number" class="form-control" id="research_credit_hours" name="research_credit_hours" min="1" max="100" required>
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="research_grade" class="form-label">
                        <i class="fas fa-star text-warning"></i>
                        الدرجة (من 100)
                    </label>
                    <input type="number" class="form-control" id="research_grade" min="0" max="100" step="0.1">
                </div>
            </div>
            <div class="col-md-4">
                <div class="mb-3">
                    <label for="research_letter_grade" class="form-label">
                        <i class="fas fa-award text-success"></i>
                        التقدير
                    </label>
                    <input type="text" class="form-control" id="research_letter_grade" readonly>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <button type="button" class="btn btn-success w-100" onclick="saveResearchGrade()">
                    <i class="fas fa-save"></i>
                    حفظ الدرجة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Grades Table -->
<div class="card" id="gradesCard" style="display: none;">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="card-title mb-0">
                <i class="fas fa-table"></i>
                درجات الطلبة
            </h5>
            <div>
                <button class="btn btn-success btn-sm" onclick="saveAllGrades()">
                    <i class="fas fa-save"></i>
                    حفظ جميع الدرجات
                </button>
            </div>
        </div>
        
        <form id="gradesForm">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th width="8%">التسلسل</th>
                            <th width="25%">اسم الطالب</th>
                            <th width="20%">درجة السعي</th>
                            <th width="20%">درجة الامتحان النهائي</th>
                            <th width="15%">الدرجة كاملة</th>
                            <th width="12%">التقدير</th>
                        </tr>
                    </thead>
                    <tbody id="studentsTableBody">
                        <!-- سيتم ملء البيانات هنا -->
                    </tbody>
                </table>
            </div>

            <!-- إحصائيات النجاح والرسوب -->
            <div class="row mt-4" id="statisticsSection" style="display: none;">
                <div class="col-12">
                    <div class="card bg-light">
                        <div class="card-body">
                            <h6 class="card-title">
                                <i class="fas fa-chart-pie"></i>
                                إحصائيات النتائج
                            </h6>
                            <div class="row text-center">
                                <div class="col-md-3">
                                    <div class="bg-success text-white p-3 rounded">
                                        <h4 id="passCount">0</h4>
                                        <small>عدد الناجحين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="bg-danger text-white p-3 rounded">
                                        <h4 id="failCount">0</h4>
                                        <small>عدد الراسبين</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="bg-primary text-white p-3 rounded">
                                        <h4 id="passPercentage">0%</h4>
                                        <small>نسبة النجاح</small>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="bg-warning text-white p-3 rounded">
                                        <h4 id="failPercentage">0%</h4>
                                        <small>نسبة الرسوب</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Loading Spinner -->
<div id="loadingSpinner" class="text-center" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">جاري التحميل...</span>
    </div>
    <p class="mt-2">جاري تحميل بيانات الطلبة...</p>
</div>



<!-- Success Modal for Save Operations -->
<div class="modal fade" id="successModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-body text-center p-5">
                <div class="mb-4">
                    <div class="success-icon-container">
                        <i class="fas fa-check-circle text-success" style="font-size: 4rem;"></i>
                        <div class="success-animation"></div>
                    </div>
                </div>
                <h4 class="text-success mb-3">تم الحفظ بنجاح!</h4>
                <p class="text-muted mb-4" id="successMessage">تم حفظ جميع الدرجات بنجاح</p>
                <button type="button" class="btn btn-success px-4" data-bs-dismiss="modal">
                    <i class="fas fa-thumbs-up me-2"></i>
                    ممتاز
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal for Error Operations -->
<div class="modal fade" id="errorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow-lg">
            <div class="modal-body text-center p-5">
                <div class="mb-4">
                    <i class="fas fa-exclamation-triangle text-danger" style="font-size: 4rem;"></i>
                </div>
                <h4 class="text-danger mb-3">حدث خطأ!</h4>
                <p class="text-muted mb-4" id="errorMessage">حدث خطأ أثناء العملية</p>
                <button type="button" class="btn btn-danger px-4" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>
                    إغلاق
                </button>
            </div>
        </div>
    </div>
</div>



{% endblock %}

{% block scripts %}
<script>
// متغيرات عامة
let currentStudents = [];
let currentFilters = {};
let allSemesters = {{ semesters|tojson }};
let allSubjects = {{ subjects|tojson }};

// تحميل الطلبة عند تغيير الفلاتر
document.getElementById('filterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    loadStudents();
});

// تحميل الفصول الدراسية حسب العام المختار
function loadSemesters() {
    const academicYearId = document.getElementById('academic_year').value;
    const semesterSelect = document.getElementById('semester');

    // مسح الخيارات الحالية
    semesterSelect.innerHTML = '<option value="">اختر الفصل الدراسي</option>';

    if (academicYearId) {
        // تفعيل القائمة
        semesterSelect.disabled = false;

        // تحميل الفصول المرتبطة بالعام الدراسي
        fetch('/api/semesters/by-year', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({academic_year_id: academicYearId})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                data.semesters.forEach(semester => {
                    const option = document.createElement('option');
                    option.value = semester.semester_id;
                    option.textContent = semester.semester_name;
                    semesterSelect.appendChild(option);
                });
            } else {
                // في حالة عدم وجود فصول، استخدم جميع الفصول
                allSemesters.forEach(semester => {
                    const option = document.createElement('option');
                    option.value = semester.semester_id;
                    option.textContent = semester.semester_name;
                    semesterSelect.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading semesters:', error);
            // في حالة الخطأ، استخدم جميع الفصول
            allSemesters.forEach(semester => {
                const option = document.createElement('option');
                option.value = semester.semester_id;
                option.textContent = semester.semester_name;
                semesterSelect.appendChild(option);
            });
        });
    } else {
        // تعطيل القائمة
        semesterSelect.disabled = true;
        semesterSelect.innerHTML = '<option value="">اختر العام الدراسي أولاً</option>';
    }
}

// تحميل المواد حسب البرنامج الأكاديمي المختار
function loadSubjects() {
    const programId = document.getElementById('academic_program').value;
    const academicYearId = document.getElementById('academic_year').value;
    const subjectSelect = document.getElementById('subject');

    // مسح الخيارات الحالية
    subjectSelect.innerHTML = '<option value="">اختر المادة</option>';

    if (programId && academicYearId) {
        // تفعيل القائمة
        subjectSelect.disabled = false;

        // تحميل المواد المرتبطة بالبرنامج الأكاديمي والعام الدراسي
        fetch('/api/subjects/by-program-year', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                program_id: programId,
                academic_year_id: academicYearId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.subjects.length > 0) {
                data.subjects.forEach(subject => {
                    const option = document.createElement('option');
                    option.value = subject.subject_id;
                    option.textContent = subject.subject_name;
                    subjectSelect.appendChild(option);
                });
            } else {
                // في حالة عدم وجود مواد محددة، استخدم المواد المرتبطة بالبرنامج فقط
                fetch('/api/subjects/by-program', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({program_id: programId})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        data.subjects.forEach(subject => {
                            const option = document.createElement('option');
                            option.value = subject.subject_id;
                            option.textContent = subject.subject_name;
                            subjectSelect.appendChild(option);
                        });
                    }
                })
                .catch(error => {
                    console.error('Error loading subjects by program:', error);
                });
            }
        })
        .catch(error => {
            console.error('Error loading subjects:', error);
            // في حالة الخطأ، استخدم جميع المواد
            allSubjects.forEach(subject => {
                const option = document.createElement('option');
                option.value = subject.subject_id;
                option.textContent = subject.subject_name;
                subjectSelect.appendChild(option);
            });
        });
    } else {
        // تعطيل القائمة
        subjectSelect.disabled = true;
        if (!academicYearId) {
            subjectSelect.innerHTML = '<option value="">اختر العام الدراسي أولاً</option>';
        } else {
            subjectSelect.innerHTML = '<option value="">اختر البرنامج الأكاديمي أولاً</option>';
        }
    }
}

// تحديث المواد عند تغيير العام الدراسي
function updateSubjects() {
    const academicYearId = document.getElementById('academic_year').value;
    const programId = document.getElementById('academic_program').value;

    if (academicYearId && programId) {
        loadSubjects();
    } else {
        // إعادة تعيين قائمة المواد
        const subjectSelect = document.getElementById('subject');
        subjectSelect.disabled = true;
        subjectSelect.innerHTML = '<option value="">اختر العام الدراسي والبرنامج الأكاديمي أولاً</option>';
    }
}

// تحميل الطلبة
function loadStudents() {
    // التحقق من وجود العناصر أولاً
    const academicYearElement = document.getElementById('academic_year');
    const semesterElement = document.getElementById('semester');
    const academicProgramElement = document.getElementById('academic_program');
    const subjectElement = document.getElementById('subject');

    if (!academicYearElement || !semesterElement || !academicProgramElement || !subjectElement) {
        return;
    }

    const filters = {
        academic_year: academicYearElement.value,
        semester: semesterElement.value,
        academic_program: academicProgramElement.value,
        subject: subjectElement.value
    };

    // التحقق من اكتمال البيانات
    if (!filters.academic_year || !filters.semester || !filters.academic_program || !filters.subject) {
        return;
    }

    currentFilters = filters;

    // عرض مؤشر التحميل
    document.getElementById('loadingSpinner').style.display = 'block';
    document.getElementById('gradesCard').style.display = 'none';

    console.log('Sending filters:', filters);

    fetch('/api/grades/students', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(filters)
    })
    .then(response => {
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.log('Response data:', data);

        if (data.success) {
            currentStudents = data.students;
            console.log('Students loaded:', data.students.length);
            displayStudents(data.students);
            document.getElementById('gradesCard').style.display = 'block';

            // تم حذف الإشعارات
        } else {
            // عرض خطأ في منتصف الصفحة
            showErrorModal('خطأ في تحميل بيانات الطلبة: ' + (data.error || 'خطأ غير معروف'));
        }
    })
    .catch(error => {
        document.getElementById('loadingSpinner').style.display = 'none';
        console.error('Detailed error:', error);
        showErrorModal('خطأ في الاتصال بالخادم: ' + error.message);
    });
}

// عرض الطلبة في الجدول
function displayStudents(students) {
    const tbody = document.getElementById('studentsTableBody');
    tbody.innerHTML = '';
    
    students.forEach((student, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="text-center fw-bold">${index + 1}</td>
            <td>
                <div class="fw-bold">${student.student_name}</div>
                <small class="text-muted">رقم الطالب: ${student.student_number}</small>
            </td>
            <td>
                <input type="number" class="form-control grade-input" 
                       id="coursework_${student.student_id}" 
                       min="0" max="100" step="0.25" 
                       value="${student.coursework_grade || ''}"
                       placeholder="درجة السعي"
                       onchange="calculateTotal(${student.student_id})">
            </td>
            <td>
                <input type="number" class="form-control grade-input" 
                       id="final_${student.student_id}" 
                       min="0" max="100" step="0.25" 
                       value="${student.final_exam_grade || ''}"
                       placeholder="درجة الامتحان النهائي"
                       onchange="calculateTotal(${student.student_id})">
            </td>
            <td>
                <input type="number" class="form-control total-grade" 
                       id="total_${student.student_id}" 
                       readonly value="${student.total_grade || ''}">
            </td>
            <td>
                <span class="badge grade-status" id="status_${student.student_id}">
                    ${student.grade_letter || ''}
                </span>
            </td>
        `;
        tbody.appendChild(row);
        
        // حساب الدرجة الإجمالية إذا كانت موجودة
        if (student.coursework_grade || student.final_exam_grade) {
            calculateTotal(student.student_id);
        }
    });

    // عرض قسم الإحصائيات
    document.getElementById('statisticsSection').style.display = 'block';

    // حساب الإحصائيات الأولية
    calculateStatistics();
}

// حساب الدرجة الإجمالية والتقدير
function calculateTotal(studentId) {
    const courseworkElement = document.getElementById(`coursework_${studentId}`);
    const finalElement = document.getElementById(`final_${studentId}`);
    const totalElement = document.getElementById(`total_${studentId}`);
    const statusElement = document.getElementById(`status_${studentId}`);

    if (!courseworkElement || !finalElement || !totalElement || !statusElement) {
        console.error(`Missing elements for student ${studentId}`);
        return;
    }

    const coursework = parseFloat(courseworkElement.value) || 0;
    const final = parseFloat(finalElement.value) || 0;
    const total = coursework + final;

    totalElement.value = total.toFixed(2);

    // حساب التقدير
    let grade_letter = '';
    let badgeClass = '';

    if (total >= 90) {
        grade_letter = 'امتياز';
        badgeClass = 'bg-success';
    } else if (total >= 80) {
        grade_letter = 'جيد جداً';
        badgeClass = 'bg-primary';
    } else if (total >= 70) {
        grade_letter = 'جيد';
        badgeClass = 'bg-info';
    } else if (total >= 60) {
        grade_letter = 'مقبول';
        badgeClass = 'bg-warning';
    } else if (coursework > 0 || final > 0) {
        grade_letter = 'راسب';
        badgeClass = 'bg-danger';
    }

    statusElement.textContent = grade_letter;
    statusElement.className = `badge grade-status ${badgeClass}`;



    // تحديث الإحصائيات
    calculateStatistics();
}

// حساب إحصائيات النجاح والرسوب
function calculateStatistics() {
    let passCount = 0;
    let failCount = 0;
    let totalStudents = 0;

    if (!currentStudents || currentStudents.length === 0) {
        // إعادة تعيين الإحصائيات إذا لم توجد طلبة
        document.getElementById('passCount').textContent = '0';
        document.getElementById('failCount').textContent = '0';
        document.getElementById('passPercentage').textContent = '0%';
        document.getElementById('failPercentage').textContent = '0%';
        return;
    }

    currentStudents.forEach(student => {
        const courseworkElement = document.getElementById(`coursework_${student.student_id}`);
        const finalElement = document.getElementById(`final_${student.student_id}`);

        if (!courseworkElement || !finalElement) {
            return; // تخطي هذا الطالب إذا لم توجد العناصر
        }

        const coursework = parseFloat(courseworkElement.value) || 0;
        const final = parseFloat(finalElement.value) || 0;
        const total = coursework + final;

        // إذا تم إدخال أي درجة
        if (coursework > 0 || final > 0) {
            totalStudents++;
            if (total >= 60) {
                passCount++;
            } else {
                failCount++;
            }
        }
    });

    // حساب النسب
    const passPercentage = totalStudents > 0 ? ((passCount / totalStudents) * 100).toFixed(1) : 0;
    const failPercentage = totalStudents > 0 ? ((failCount / totalStudents) * 100).toFixed(1) : 0;

    // تحديث العرض
    document.getElementById('passCount').textContent = passCount;
    document.getElementById('failCount').textContent = failCount;
    document.getElementById('passPercentage').textContent = passPercentage + '%';
    document.getElementById('failPercentage').textContent = failPercentage + '%';
}

// حفظ جميع الدرجات
function saveAllGrades() {
    const gradesData = [];

    if (!currentStudents || currentStudents.length === 0) {
        return;
    }

    currentStudents.forEach(student => {
        const courseworkElement = document.getElementById(`coursework_${student.student_id}`);
        const finalElement = document.getElementById(`final_${student.student_id}`);

        if (!courseworkElement || !finalElement) {
            console.warn(`Missing elements for student ${student.student_id}`);
            return;
        }

        const coursework = parseFloat(courseworkElement.value) || 0;
        const final = parseFloat(finalElement.value) || 0;
        const total = coursework + final;

        // حساب التقدير
        let grade_letter = '';
        if (total >= 90) grade_letter = 'امتياز';
        else if (total >= 80) grade_letter = 'جيد جداً';
        else if (total >= 70) grade_letter = 'جيد';
        else if (total >= 60) grade_letter = 'مقبول';
        else if (coursework > 0 || final > 0) grade_letter = 'راسب';

        if (coursework > 0 || final > 0) {
            gradesData.push({
                student_id: student.student_id,
                academic_year_id: currentFilters.academic_year,
                semester_id: currentFilters.semester,
                academic_program_id: currentFilters.academic_program,
                subject_id: currentFilters.subject,
                coursework_grade: coursework,
                final_exam_grade: final,
                total_grade: total,
                grade_letter: grade_letter
            });
        }
    });

    if (gradesData.length === 0) {
        return;
    }

    // عرض شريط التقدم
    showSaveProgress();

    fetch('/api/grades/save-all', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({grades: gradesData})
    })
    .then(response => response.json())
    .then(data => {
        // إخفاء شريط التقدم
        hideSaveProgress();

        if (data.success) {
            showSuccessModal(`تم حفظ ${data.saved_count} درجة بنجاح`);

            // تحديث الإحصائيات
            setTimeout(() => {
                calculateStatistics();
            }, 500);
        } else {
            showErrorModal('خطأ في حفظ الدرجات: ' + data.error);
        }
    })
    .catch(error => {
        // إخفاء شريط التقدم
        hideSaveProgress();

        showErrorModal('خطأ في الاتصال بالخادم');
        console.error('Error:', error);
    });
}

// مسح المعايير
function clearFilters() {
    document.getElementById('filterForm').reset();
    document.getElementById('gradesCard').style.display = 'none';
    document.getElementById('statisticsSection').style.display = 'none';
    currentStudents = [];
    currentFilters = {};

    // إعادة تعيين حالة القوائم المنسدلة
    const semesterSelect = document.getElementById('semester');
    const subjectSelect = document.getElementById('subject');

    semesterSelect.disabled = true;
    semesterSelect.innerHTML = '<option value="">اختر العام الدراسي أولاً</option>';

    subjectSelect.disabled = true;
    subjectSelect.innerHTML = '<option value="">اختر البرنامج الأكاديمي أولاً</option>';
}

// عرض/إخفاء المعايير
function showFilters() {
    const filtersCard = document.getElementById('filtersCard');
    if (filtersCard.style.display === 'none') {
        filtersCard.style.display = 'block';
    } else {
        filtersCard.style.display = 'none';
    }
}



// عرض نافذة النجاح
function showSuccessModal(message) {
    document.getElementById('successMessage').textContent = message;
    const successModal = new bootstrap.Modal(document.getElementById('successModal'));
    successModal.show();

    // إغلاق تلقائي بعد 3 ثوان
    setTimeout(() => {
        successModal.hide();
    }, 3000);
}



// عرض شريط التقدم
function showSaveProgress() {
    const progressBar = document.getElementById('saveProgressBar');
    progressBar.classList.add('active');
}

// إخفاء شريط التقدم
function hideSaveProgress() {
    const progressBar = document.getElementById('saveProgressBar');
    setTimeout(() => {
        progressBar.classList.remove('active');
    }, 500);
}



// عرض modal الأخطاء
function showErrorModal(message) {
    document.getElementById('errorMessage').textContent = message;
    const errorModal = new bootstrap.Modal(document.getElementById('errorModal'));
    errorModal.show();
}

// متغيرات السنة البحثية
let currentResearchFilters = {};

// تحميل الطلبة عند تغيير العام الدراسي أو البرنامج الأكاديمي في السنة البحثية
document.getElementById('research_academic_year').addEventListener('change', function() {
    loadResearchStudents();
});

document.getElementById('research_academic_program').addEventListener('change', function() {
    loadResearchStudents();
});

// تحميل الدرجة عند اختيار الطالب
document.getElementById('research_student').addEventListener('change', function() {
    loadExistingResearchGrade();
});

// تحديث عدد الوحدات عند اختيار المادة (كقيمة افتراضية قابلة للتعديل)
document.getElementById('research_subject').addEventListener('change', function() {
    const subjectName = this.value;

    if (subjectName) {
        // إظهار نموذج إدخال الدرجة وتحديث عدد الوحدات
        document.getElementById('researchGradeCard').style.display = 'block';
        const creditHoursElement = document.getElementById('research_credit_hours');
        creditHoursElement.value = getSubjectCreditHours(subjectName);
        // تحميل الدرجة إذا كان الطالب مختار
        loadExistingResearchGrade();
    } else {
        document.getElementById('researchGradeCard').style.display = 'none';
    }
});

// تحميل طلبة السنة البحثية
function loadResearchStudents() {
    const yearId = document.getElementById('research_academic_year').value;
    const programId = document.getElementById('research_academic_program').value;
    const studentSelect = document.getElementById('research_student');

    if (!yearId || !programId) {
        studentSelect.innerHTML = '<option value="">اختر العام الدراسي والبرنامج الأكاديمي أولاً</option>';
        studentSelect.disabled = true;
        return;
    }

    // عرض رسالة تحميل
    studentSelect.innerHTML = '<option value="">جاري تحميل الطلبة...</option>';
    studentSelect.disabled = true;

    // جلب الطلبة من API
    fetch(`/api/students?program_id=${programId}&year_id=${yearId}`)
        .then(response => response.json())
        .then(data => {
            studentSelect.innerHTML = '<option value="">اختر الطالب</option>';
            if (data && Array.isArray(data) && data.length > 0) {
                data.forEach(student => {
                    studentSelect.innerHTML += `<option value="${student.student_id}">${student.name}</option>`;
                });
                studentSelect.disabled = false;
            } else {
                studentSelect.innerHTML = '<option value="">لا توجد طلبة في هذا العام والبرنامج</option>';
                studentSelect.disabled = true;
            }
        })
        .catch(error => {
            console.error('Error loading students:', error);
            studentSelect.innerHTML = '<option value="">خطأ في تحميل الطلبة</option>';
            studentSelect.disabled = true;
        });
}

// معالجة نموذج السنة البحثية - لم نعد نحتاجها لأن الدرجة تظهر تلقائياً
document.getElementById('researchFilterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    // لا نحتاج لفعل شيء، الدرجة تظهر تلقائياً عند اختيار الطالب
});

// تحديث التقدير عند تغيير الدرجة
document.getElementById('research_grade').addEventListener('input', function() {
    const grade = parseFloat(this.value) || 0;
    const letterGradeElement = document.getElementById('research_letter_grade');

    let letterGrade = '';
    if (grade >= 90) {
        letterGrade = 'امتياز';
    } else if (grade >= 80) {
        letterGrade = 'جيد جداً';
    } else if (grade >= 70) {
        letterGrade = 'جيد';
    } else if (grade >= 60) {
        letterGrade = 'مقبول';
    } else {
        letterGrade = 'راسب';
    }

    letterGradeElement.value = letterGrade;
});

// حفظ درجة السنة البحثية
function saveResearchGrade() {
    const creditHours = parseInt(document.getElementById('research_credit_hours').value);
    const grade = parseFloat(document.getElementById('research_grade').value);
    const letterGrade = document.getElementById('research_letter_grade').value;

    if (!creditHours || creditHours < 1 || creditHours > 100) {
        showErrorModal('يرجى إدخال عدد وحدات صحيح بين 1 و 100');
        return;
    }

    if (!grade || grade < 0 || grade > 100) {
        showErrorModal('يرجى إدخال درجة صحيحة بين 0 و 100');
        return;
    }

    const studentSelect = document.getElementById('research_student');
    const studentName = studentSelect.options[studentSelect.selectedIndex].text;
    const subjectName = currentResearchFilters.subject;

    const data = {
        academic_year_id: currentResearchFilters.academic_year,
        academic_program_id: currentResearchFilters.academic_program,
        subject_name: currentResearchFilters.subject,
        credit_hours: creditHours,
        student_name: studentName,
        grade: grade
    };

    fetch('/api/research_subjects', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccessModal(data.message);
            // لا نمسح الفلاتر، فقط نبقي على الدرجة المحفوظة
        } else {
            showErrorModal(data.error);
        }
    })
    .catch(error => {
        showErrorModal('خطأ في الاتصال بالخادم');
        console.error('Error:', error);
    });
}

// الحصول على عدد الوحدات حسب نوع المادة
function getSubjectCreditHours(subjectName) {
    switch(subjectName) {
        case 'السمنار': return 2;
        case 'الرسالة': return 6;
        case 'الاطروحة': return 12;
        case 'مشروع بحث': return 4;
        default: return 0;
    }
}

// تحميل الدرجة الموجودة للطالب
function loadExistingResearchGrade() {
    const academicYear = document.getElementById('research_academic_year').value;
    const academicProgram = document.getElementById('research_academic_program').value;
    const subject = document.getElementById('research_subject').value;
    const studentSelect = document.getElementById('research_student');
    const studentId = studentSelect.value;

    if (!academicYear || !academicProgram || !subject || !studentId) {
        // إخفاء قسم الدرجة إذا لم تكن جميع المعايير مختارة
        document.getElementById('researchGradeCard').style.display = 'none';
        return;
    }

    // إظهار قسم إدخال الدرجة عند توفر جميع المعايير
    document.getElementById('researchGradeCard').style.display = 'block';

    const studentName = studentSelect.options[studentSelect.selectedIndex].text;

    // تحديث المتغيرات أولاً
    currentResearchFilters = {
        academic_year: academicYear,
        academic_program: academicProgram,
        subject: subject,
        student: studentId
    };

    // جلب الدرجة الموجودة
    fetch(`/api/research_subjects/grade?academic_year_id=${academicYear}&academic_program_id=${academicProgram}&subject_name=${encodeURIComponent(subject)}&student_name=${encodeURIComponent(studentName)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success && data.grade !== null) {
                // عرض البيانات الموجودة
                document.getElementById('research_credit_hours').value = data.credit_hours;
                document.getElementById('research_grade').value = data.grade;
                document.getElementById('research_letter_grade').value = data.letter_grade;
            } else {
                // مسح الحقول للطالب الجديد
                document.getElementById('research_grade').value = '';
                document.getElementById('research_letter_grade').value = '';
                // الاحتفاظ بعدد الوحدات الافتراضي
                if (!document.getElementById('research_credit_hours').value) {
                    document.getElementById('research_credit_hours').value = getSubjectCreditHours(subject);
                }
            }
        })
        .catch(error => {
            console.error('Error loading existing grade:', error);
            // في حالة الخطأ، مسح الحقول
            document.getElementById('research_grade').value = '';
            document.getElementById('research_letter_grade').value = '';
        });
}

// مسح معايير السنة البحثية
function clearResearchFilters() {
    document.getElementById('researchFilterForm').reset();
    document.getElementById('researchGradeCard').style.display = 'none';
    document.getElementById('research_student').innerHTML = '<option value="">اختر العام الدراسي والبرنامج الأكاديمي أولاً</option>';
    document.getElementById('research_student').disabled = true;
    currentResearchFilters = {};
}

</script>
{% endblock %}
