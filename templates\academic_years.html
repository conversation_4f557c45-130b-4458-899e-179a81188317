{% extends "base.html" %}

{% block title %}إدارة الأعوام الدراسية - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    /* تحسين مظهر الجدول */
    .table-dark th {
        background-color: #343a40 !important;
        color: #fff !important;
        font-weight: 600;
        border-bottom: 2px solid #495057;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background-color: rgba(0, 0, 0, 0.02);
    }

    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.1);
        transition: background-color 0.15s ease-in-out;
    }

    .btn-group .btn {
        margin: 0 2px;
    }

    .fw-semibold {
        font-weight: 600;
    }

    .text-primary {
        color: #0d6efd !important;
    }

    .text-muted {
        color: #6c757d !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-calendar-alt"></i>
        إدارة الأعوام الدراسية
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات الأعوام الدراسية</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addYearModal">
        <i class="fas fa-plus"></i>
        إضافة عام دراسي جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في الأعوام الدراسية...">
            </div>
            <div class="col-md-4">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Academic Years Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="yearsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>العام الدراسي</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="yearsTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Academic Year Modal -->
<div class="modal fade" id="addYearModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة عام دراسي جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="yearForm">
                    <input type="hidden" id="yearId" name="year_id">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">العام الدراسي *</label>
                        <input type="text" class="form-control" id="name" name="name" required maxlength="255" 
                               placeholder="مثال: 2024-2025">
                        <div class="form-text">أدخل العام الدراسي بالصيغة: سنة البداية - سنة النهاية</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveYear()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let academicYears = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadAcademicYears();
});

// تحميل قائمة الأعوام الدراسية
async function loadAcademicYears() {
    try {
        console.log('🔄 بدء تحميل الأعوام الدراسية...');

        const response = await fetch('/api/academic_years');
        console.log('📡 استجابة الأعوام الدراسية:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 الأعوام الدراسية المستلمة:', data);
        console.log('📊 نوع البيانات:', typeof data);
        console.log('📊 هل هي مصفوفة؟', Array.isArray(data));

        if (Array.isArray(data)) {
            academicYears = data;
            console.log('✅ تم تحميل', data.length, 'عام دراسي');
            displayAcademicYears(data);
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showAlert('خطأ في تحميل بيانات الأعوام الدراسية: ' + (data.error || 'بيانات غير صحيحة'), 'error');
        }

    } catch (error) {
        console.error('❌ خطأ في تحميل الأعوام الدراسية:', error);
        showAlert('حدث خطأ أثناء تحميل بيانات الأعوام الدراسية: ' + error.message, 'error');
    }
}

// عرض الأعوام الدراسية في الجدول
function displayAcademicYears(data) {
    console.log('🎨 عرض الأعوام الدراسية:', data.length, 'عام');
    const tbody = document.getElementById('yearsTableBody');

    if (!tbody) {
        console.error('❌ عنصر الجدول غير موجود!');
        return;
    }

    tbody.innerHTML = '';

    if (!data || data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center py-5">
                    <div class="text-muted">
                        <i class="fas fa-calendar-alt fa-3x mb-3"></i>
                        <h5>لا توجد أعوام دراسية مسجلة حالياً</h5>
                        <p>يمكنك إضافة أول عام دراسي باستخدام الزر أعلاه</p>
                    </div>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((year, index) => {
        console.log(`📋 عام ${index + 1}:`, year);

        const row = document.createElement('tr');
        const createdDate = year.created_date ?
            new Date(year.created_date).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) : '-';

        const yearName = year.name || 'غير محدد';
        console.log(`📝 اسم العام: "${yearName}"`);

        row.innerHTML = `
            <td class="text-center fw-bold">${index + 1}</td>
            <td>
                <div class="d-flex align-items-center">
                    <i class="fas fa-calendar-alt text-primary me-2"></i>
                    <span class="fw-semibold">${yearName}</span>
                </div>
            </td>
            <td>
                <small class="text-muted">
                    <i class="fas fa-calendar-plus me-1"></i>
                    ${createdDate}
                </small>
            </td>
            <td class="text-center">
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-outline-primary" onclick="editYear(${year.year_id})" title="تعديل العام الدراسي">
                        <i class="fas fa-edit"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="deleteYear(${year.year_id}, '${yearName.replace(/'/g, "\\'")}'))" title="حذف العام الدراسي">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });

    console.log('✅ تم عرض', data.length, 'عام دراسي بنجاح');
}

// إضافة أو تحديث عام دراسي
function saveYear() {
    const form = document.getElementById('yearForm');
    const yearId = document.getElementById('yearId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();

    if (!name) {
        showAlert('اسم العام الدراسي مطلوب', 'error');
        return;
    }

    const data = {
        name: name
    };

    const url = yearId ? `/api/academic_years/${yearId}` : '/api/academic_years';
    const method = yearId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addYearModal')).hide();
            form.reset();
            document.getElementById('yearId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة عام دراسي جديد';
            loadAcademicYears();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ العام الدراسي', 'error');
    });
}

// تعديل عام دراسي
function editYear(yearId) {
    const year = academicYears.find(y => y.year_id === yearId);
    if (!year) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('yearId').value = year.year_id;
    document.getElementById('name').value = year.name;
    document.getElementById('modalTitle').textContent = 'تعديل بيانات العام الدراسي';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addYearModal')).show();
}

// حذف عام دراسي
function deleteYear(yearId, yearName) {
    if (!confirm(`هل أنت متأكد من حذف العام الدراسي "${yearName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/academic_years/${yearId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadAcademicYears();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف العام الدراسي', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterYears();
});

function filterYears() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    const filteredData = academicYears.filter(year => {
        return year.name.toLowerCase().includes(searchTerm);
    });

    displayAcademicYears(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    displayAcademicYears(academicYears);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addYearModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('yearForm').reset();
            document.getElementById('yearId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة عام دراسي جديد';
        });
    }
});
</script>
{% endblock %}
