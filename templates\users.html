{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    .user-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    
    .user-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .user-header {
        background: linear-gradient(135deg, #495057 0%, #343a40 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .user-body {
        padding: 20px;
    }
    
    .user-type-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .type-admin { background-color: #dc3545; color: white; }
    .type-manager { background-color: #fd7e14; color: white; }
    .type-employee { background-color: #198754; color: white; }
    .type-user { background-color: #6c757d; color: white; }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-active { background-color: #d1edff; color: #0c63e4; }
    .status-inactive { background-color: #f8d7da; color: #721c24; }
    
    .info-row {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 120px;
        margin-left: 10px;
    }
    
    .info-value {
        color: #212529;
        flex: 1;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-users"></i>
        إدارة المستخدمين
    </h1>
    <p class="page-subtitle">إدارة حسابات المستخدمين - عرض وإضافة وتعديل وحذف</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addUserModal">
        <i class="fas fa-user-plus"></i>
        إضافة مستخدم جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-secondary" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
        تحديث
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث في الأسماء</label>
                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن مستخدم..." onkeyup="filterUsers()">
            </div>
            <div class="col-md-2">
                <label class="form-label">نوع المستخدم</label>
                <select class="form-select" id="userTypeFilter" onchange="filterUsers()">
                    <option value="">جميع الأنواع</option>
                    <option value="مدير النظام">مدير النظام</option>
                    <option value="مدير">مدير</option>
                    <option value="موظف">موظف</option>
                    <option value="مستخدم عادي">مستخدم عادي</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter" onchange="filterUsers()">
                    <option value="">جميع الحالات</option>
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">البحث في البريد الإلكتروني</label>
                <input type="text" class="form-control" id="emailSearchInput" placeholder="ابحث في البريد الإلكتروني..." onkeyup="filterUsers()">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4" id="statisticsCards">
    <!-- سيتم تحميل الإحصائيات هنا -->
</div>

<!-- Users Container -->
<div id="usersContainer">
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-3 text-muted">جاري تحميل بيانات المستخدمين...</p>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    إضافة مستخدم جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addUserForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">اسم المستخدم *</label>
                            <input type="text" class="form-control" id="username" name="username" required maxlength="100">
                            <div class="form-text">يجب أن يكون فريداً</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required maxlength="255">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">كلمة المرور *</label>
                            <input type="password" class="form-control" id="password" name="password" required minlength="6">
                            <div class="form-text">الحد الأدنى 6 أحرف</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="user_type" class="form-label">نوع المستخدم *</label>
                            <select class="form-select" id="user_type" name="user_type" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="مدير النظام">مدير النظام</option>
                                <option value="مدير">مدير</option>
                                <option value="موظف">موظف</option>
                                <option value="مستخدم عادي">مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">الحالة</label>
                            <select class="form-select" id="status" name="status">
                                <option value="نشط" selected>نشط</option>
                                <option value="غير نشط">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="phone" name="phone" maxlength="20">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="addUser()">
                    <i class="fas fa-save"></i>
                    حفظ المستخدم
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit User Modal -->
<div class="modal fade" id="editUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i>
                    تعديل بيانات المستخدم
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editUserForm">
                    <input type="hidden" id="edit_user_id" name="user_id">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_username" class="form-label">اسم المستخدم *</label>
                            <input type="text" class="form-control" id="edit_username" name="username" required maxlength="100">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_full_name" class="form-label">الاسم الكامل *</label>
                            <input type="text" class="form-control" id="edit_full_name" name="full_name" required maxlength="255">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_user_type" class="form-label">نوع المستخدم *</label>
                            <select class="form-select" id="edit_user_type" name="user_type" required>
                                <option value="">اختر نوع المستخدم</option>
                                <option value="مدير النظام">مدير النظام</option>
                                <option value="مدير">مدير</option>
                                <option value="موظف">موظف</option>
                                <option value="مستخدم عادي">مستخدم عادي</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_status" class="form-label">الحالة</label>
                            <select class="form-select" id="edit_status" name="status">
                                <option value="نشط">نشط</option>
                                <option value="غير نشط">غير نشط</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="edit_email" name="email" maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="edit_phone" name="phone" maxlength="20">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="updateUser()">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها!
                </div>
                <p>هل أنت متأكد من حذف بيانات المستخدم؟</p>
                <div id="deleteUserInfo" class="bg-light p-3 rounded">
                    <!-- سيتم عرض معلومات المستخدم هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let users = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة المستخدمين...');

    // تحميل فوري
    loadUsers();

    // تحميل احتياطي بعد ثانية واحدة
    setTimeout(function() {
        if (users.length === 0) {
            console.log('🔄 إعادة محاولة تحميل المستخدمين...');
            loadUsers();
        }
    }, 1000);
});

// تحميل قائمة المستخدمين
async function loadUsers() {
    try {
        console.log('🔄 تحميل المستخدمين...');

        // عرض رسالة التحميل
        document.getElementById('usersContainer').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3 text-muted">جاري تحميل بيانات المستخدمين...</p>
            </div>
        `;

        const response = await fetch('/api/users');
        console.log('📡 استجابة المستخدمين:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 المستخدمين المستلمة:', data);
        console.log('📊 نوع البيانات:', typeof data);
        console.log('📊 هل هي مصفوفة؟', Array.isArray(data));

        if (Array.isArray(data) && data.length > 0) {
            users = data;
            console.log('✅ تم تحميل', users.length, 'مستخدم');
            displayUsers(users);
            updateStatistics();
        } else if (Array.isArray(data) && data.length === 0) {
            console.log('⚠️ لا توجد مستخدمين في قاعدة البيانات');
            showNoUsers();
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showError('خطأ في تحميل بيانات المستخدمين: ' + (data.error || 'بيانات غير صحيحة'));
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل المستخدمين:', error);
        showError('حدث خطأ أثناء تحميل بيانات المستخدمين: ' + error.message);
    }
}

// عرض المستخدمين
function displayUsers(usersToDisplay) {
    console.log('🎨 عرض المستخدمين:', usersToDisplay.length, 'مستخدم');
    const container = document.getElementById('usersContainer');

    if (!container) {
        console.error('❌ عنصر usersContainer غير موجود!');
        return;
    }

    if (!usersToDisplay || usersToDisplay.length === 0) {
        console.log('⚠️ لا توجد مستخدمين للعرض');
        showNoUsers();
        return;
    }

    let html = '<div class="row">';
    
    usersToDisplay.forEach(user => {
        const userTypeClass = getUserTypeClass(user.user_type);
        const statusClass = getStatusClass(user.status);
        const createdDate = user.created_date ? new Date(user.created_date).toLocaleDateString('ar-SA') : 'غير محدد';

        html += `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="user-card">
                    <div class="user-header">
                        <div>
                            <h6 class="mb-1">${user.full_name}</h6>
                            <small>@${user.username}</small>
                        </div>
                        <div class="text-left">
                            <span class="user-type-badge ${userTypeClass}">${user.user_type}</span>
                        </div>
                    </div>
                    <div class="user-body">
                        <div class="info-row">
                            <span class="info-label">رقم المستخدم:</span>
                            <span class="info-value">${user.user_id}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">الحالة:</span>
                            <span class="info-value">
                                <span class="status-badge ${statusClass}">${user.status}</span>
                            </span>
                        </div>
                        ${user.email ? `
                        <div class="info-row">
                            <span class="info-label">البريد:</span>
                            <span class="info-value">${user.email}</span>
                        </div>
                        ` : ''}
                        ${user.phone ? `
                        <div class="info-row">
                            <span class="info-label">الهاتف:</span>
                            <span class="info-value">${user.phone}</span>
                        </div>
                        ` : ''}
                        <div class="info-row">
                            <span class="info-label">تاريخ الإنشاء:</span>
                            <span class="info-value">${createdDate}</span>
                        </div>
                        
                        <div class="d-flex gap-2 mt-3">
                            <button class="btn btn-sm btn-outline-primary" onclick="editUser(${user.user_id})">
                                <i class="fas fa-edit"></i>
                                تعديل
                            </button>
                            <button class="btn btn-sm btn-outline-warning" onclick="resetPassword(${user.user_id})">
                                <i class="fas fa-key"></i>
                                إعادة تعيين
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser(${user.user_id})">
                                <i class="fas fa-trash"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    container.innerHTML = html;
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('📊 تحديث الإحصائيات...');

    if (!users || users.length === 0) {
        console.log('⚠️ لا توجد بيانات مستخدمين للإحصائيات');
        document.getElementById('statisticsCards').innerHTML = '';
        return;
    }

    const totalUsers = users.length;
    const activeUsers = users.filter(u => u.status === 'نشط').length;
    const inactiveUsers = users.filter(u => u.status === 'غير نشط').length;

    const adminUsers = users.filter(u => u.user_type === 'مدير النظام').length;
    const managerUsers = users.filter(u => u.user_type === 'مدير').length;

    console.log(`📊 إحصائيات: إجمالي=${totalUsers}, نشط=${activeUsers}, غير نشط=${inactiveUsers}`);

    const html = `
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${totalUsers}</h4>
                            <p class="mb-0">إجمالي المستخدمين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${activeUsers}</h4>
                            <p class="mb-0">المستخدمين النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${inactiveUsers}</h4>
                            <p class="mb-0">المستخدمين غير النشطين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-times fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${adminUsers + managerUsers}</h4>
                            <p class="mb-0">المديرين</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-shield fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('statisticsCards').innerHTML = html;
}

// دوال المساعدة
function getUserTypeClass(userType) {
    switch(userType) {
        case 'مدير النظام': return 'type-admin';
        case 'مدير': return 'type-manager';
        case 'موظف': return 'type-employee';
        case 'مستخدم عادي': return 'type-user';
        default: return 'type-user';
    }
}

function getStatusClass(status) {
    switch(status) {
        case 'نشط': return 'status-active';
        case 'غير نشط': return 'status-inactive';
        default: return 'status-inactive';
    }
}

// عرض رسالة خطأ
function showError(message) {
    document.getElementById('usersContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
            </div>
            <h4 class="text-danger">خطأ في تحميل البيانات</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadUsers()">
                <i class="fas fa-sync-alt"></i>
                إعادة المحاولة
            </button>
        </div>
    `;
}

// عرض رسالة عدم وجود مستخدمين
function showNoUsers() {
    document.getElementById('usersContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-muted mb-4">
                <i class="fas fa-users fa-3x"></i>
            </div>
            <h4 class="text-muted">لا توجد مستخدمين</h4>
            <p class="text-muted">لم يتم إنشاء أي مستخدمين في النظام بعد</p>
            <button class="btn btn-primary" onclick="showAddModal()">
                <i class="fas fa-user-plus"></i>
                إضافة أول مستخدم
            </button>
        </div>
    `;
}

// فلترة المستخدمين
function filterUsers() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const userTypeFilter = document.getElementById('userTypeFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;
    const emailSearch = document.getElementById('emailSearchInput').value.toLowerCase();

    const filteredUsers = users.filter(user => {
        const matchesSearch = user.full_name.toLowerCase().includes(searchTerm) ||
                            user.username.toLowerCase().includes(searchTerm);
        const matchesUserType = !userTypeFilter || user.user_type === userTypeFilter;
        const matchesStatus = !statusFilter || user.status === statusFilter;
        const matchesEmail = !emailSearch || (user.email && user.email.toLowerCase().includes(emailSearch));

        return matchesSearch && matchesUserType && matchesStatus && matchesEmail;
    });

    displayUsers(filteredUsers);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('userTypeFilter').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('emailSearchInput').value = '';

    displayUsers(users);
}

// إضافة مستخدم جديد
function addUser() {
    const form = document.getElementById('addUserForm');
    const formData = new FormData(form);

    // التحقق من تطابق كلمات المرور
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm_password');

    if (password !== confirmPassword) {
        showAlert('كلمات المرور غير متطابقة', 'error');
        return;
    }

    const data = {};
    formData.forEach((value, key) => {
        if (key !== 'confirm_password') {
            data[key] = value;
        }
    });

    console.log('📤 إضافة مستخدم جديد:', data);

    fetch('/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('📡 استجابة إضافة المستخدم:', response.status);
        return response.json();
    })
    .then(result => {
        console.log('📊 نتيجة إضافة المستخدم:', result);
        if (result.error) {
            showAlert('خطأ في إضافة المستخدم: ' + result.error, 'error');
        } else {
            showAlert('تم إضافة المستخدم بنجاح', 'success');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            loadUsers();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في إضافة المستخدم:', error);
        showAlert('حدث خطأ أثناء إضافة المستخدم: ' + error.message, 'error');
    });
}

// تعديل مستخدم
function editUser(userId) {
    const user = users.find(u => u.user_id === userId);
    if (!user) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('edit_user_id').value = user.user_id;
    document.getElementById('edit_username').value = user.username;
    document.getElementById('edit_full_name').value = user.full_name;
    document.getElementById('edit_user_type').value = user.user_type;
    document.getElementById('edit_status').value = user.status;
    document.getElementById('edit_email').value = user.email || '';
    document.getElementById('edit_phone').value = user.phone || '';

    new bootstrap.Modal(document.getElementById('editUserModal')).show();
}

// تحديث بيانات المستخدم
function updateUser() {
    const form = document.getElementById('editUserForm');
    const formData = new FormData(form);
    const userId = formData.get('user_id');

    const data = {};
    formData.forEach((value, key) => {
        if (key !== 'user_id') {
            data[key] = value;
        }
    });

    console.log('📤 تحديث المستخدم:', userId, data);

    fetch(`/api/users/${userId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            showAlert('خطأ في تحديث المستخدم: ' + result.error, 'error');
        } else {
            showAlert('تم تحديث بيانات المستخدم بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editUserModal')).hide();
            loadUsers();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تحديث المستخدم:', error);
        showAlert('حدث خطأ أثناء تحديث المستخدم: ' + error.message, 'error');
    });
}

// إعادة تعيين كلمة المرور
function resetPassword(userId) {
    const user = users.find(u => u.user_id === userId);
    if (!user) return;

    if (confirm(`هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم "${user.full_name}"؟\n\nسيتم تعيين كلمة المرور إلى "123456"`)) {
        fetch(`/api/users/${userId}/reset-password`, {
            method: 'POST'
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                showAlert('خطأ في إعادة تعيين كلمة المرور: ' + result.error, 'error');
            } else {
                showAlert('تم إعادة تعيين كلمة المرور بنجاح إلى "123456"', 'success');
            }
        })
        .catch(error => {
            console.error('❌ خطأ في إعادة تعيين كلمة المرور:', error);
            showAlert('حدث خطأ أثناء إعادة تعيين كلمة المرور: ' + error.message, 'error');
        });
    }
}

// تأكيد حذف المستخدم
function deleteUser(userId) {
    const user = users.find(u => u.user_id === userId);
    if (!user) return;

    const statusClass = getStatusClass(user.status);
    const userTypeClass = getUserTypeClass(user.user_type);

    document.getElementById('deleteUserInfo').innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-user me-2"></i>
            <div>
                <strong>${user.full_name}</strong><br>
                <small class="text-muted">@${user.username}</small><br>
                <span class="user-type-badge ${userTypeClass}">${user.user_type}</span>
                <span class="status-badge ${statusClass}">${user.status}</span>
            </div>
        </div>
    `;

    // تخزين ID المستخدم للحذف
    document.getElementById('confirmDeleteBtn').setAttribute('data-user-id', userId);

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// حذف المستخدم
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('confirmDeleteBtn').addEventListener('click', function() {
        const userId = this.getAttribute('data-user-id');
        if (!userId) return;

        fetch(`/api/users/${userId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                showAlert(result.error, 'error');
            } else {
                showAlert('تم حذف المستخدم بنجاح', 'success');
                bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                loadUsers();
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('حدث خطأ أثناء حذف المستخدم', 'error');
        });
    });
});

function refreshData() {
    loadUsers();
}

function exportData() {
    // تصدير البيانات إلى Excel
    const csvContent = "data:text/csv;charset=utf-8,"
        + "رقم المستخدم,اسم المستخدم,الاسم الكامل,نوع المستخدم,الحالة,البريد الإلكتروني,رقم الهاتف,تاريخ الإنشاء\n"
        + users.map(user =>
            `${user.user_id},"${user.username}","${user.full_name}","${user.user_type}","${user.status}","${user.email || ''}","${user.phone || ''}","${user.created_date || ''}"`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "users_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    // إنشاء تنبيه Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}
</script>
{% endblock %}
