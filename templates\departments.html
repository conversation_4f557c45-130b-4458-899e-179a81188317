{% extends "base.html" %}

{% block title %}إدارة الأقسام - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<style>
    .page-header {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
        backdrop-filter: blur(20px);
        border-radius: 25px;
        padding: 30px;
        margin-bottom: 30px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
    }

    .page-title {
        color: white;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }

    .data-table {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        padding: 25px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .table {
        color: white;
        margin-bottom: 0;
    }

    .table th {
        background: rgba(0, 0, 0, 0.1);
        border: none;
        color: #2c3e50;
        font-weight: bold;
        padding: 15px;
        text-align: center;
        border-bottom: 2px solid rgba(0, 0, 0, 0.2);
    }

    .table td {
        border: none;
        padding: 15px;
        text-align: center;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        vertical-align: middle;
        color: #2c3e50;
    }

    .table tbody tr:hover {
        background: rgba(52, 152, 219, 0.1);
        transform: scale(1.01);
        color: #2c3e50;
    }

    .action-btn {
        padding: 8px 15px;
        border-radius: 15px;
        border: none;
        margin: 0 3px;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .btn-edit {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
    }

    .btn-edit:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(243, 156, 18, 0.4);
    }

    .btn-delete {
        background: linear-gradient(135deg, #e74c3c, #c0392b);
        color: white;
    }

    .btn-delete:hover {
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
    }

    .modal-content {
        background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.9));
        backdrop-filter: blur(20px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .modal-header {
        background: linear-gradient(135deg, #f39c12, #e67e22);
        color: white;
        border-radius: 20px 20px 0 0;
        border-bottom: none;
    }

    .form-control, .form-select {
        border-radius: 15px;
        border: 2px solid rgba(243, 156, 18, 0.3);
        padding: 12px 15px;
        transition: all 0.3s ease;
        color: #2c3e50;
        background-color: rgba(255, 255, 255, 0.9);
    }

    .form-control:focus, .form-select:focus {
        border-color: #f39c12;
        box-shadow: 0 0 0 0.2rem rgba(243, 156, 18, 0.25);
    }

    .college-badge {
        background: linear-gradient(135deg, #27ae60, #2ecc71);
        color: white;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.85rem;
        font-weight: bold;
    }

    .form-label {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 8px;
    }

    .modal-body {
        color: #2c3e50;
    }

    .modal-title {
        color: #2c3e50;
    }
</style>

<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-sitemap"></i>
        إدارة الأقسام
    </h1>
    <p style="color: rgba(255, 255, 255, 0.8); font-size: 1.2rem;">إضافة وتعديل وحذف بيانات الأقسام العلمية</p>
</div>

<div style="display: flex; gap: 15px; margin-bottom: 30px; flex-wrap: wrap;">
    <button class="btn btn-warning" onclick="openAddModal()">
        <i class="fas fa-plus"></i>
        إضافة قسم جديد
    </button>
    <button class="btn btn-primary" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
</div>

<div class="data-table">
    <div class="table-responsive">
        <table class="table">
            <thead>
                <tr>
                    <th>اسم القسم</th>
                    <th>الكلية</th>
                    <th>رئيس القسم</th>
                    <th>مقرر القسم</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody id="departmentsTableBody">
                <tr>
                    <td><strong>قسم الطب الباطني</strong></td>
                    <td><span class="college-badge">كلية الطب</span></td>
                    <td>د. أحمد محمد علي</td>
                    <td>د. فاطمة حسن</td>
                    <td>
                        <button class="action-btn btn-edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td><strong>قسم الجراحة</strong></td>
                    <td><span class="college-badge">كلية الطب</span></td>
                    <td>د. محمد عبد الله</td>
                    <td>د. زينب أحمد</td>
                    <td>
                        <button class="action-btn btn-edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
                <tr>
                    <td><strong>قسم الهندسة المدنية</strong></td>
                    <td><span class="college-badge">كلية الهندسة</span></td>
                    <td>د. علي حسين</td>
                    <td>د. سارة محمد</td>
                    <td>
                        <button class="action-btn btn-edit">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn btn-delete">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Add/Edit Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="departmentForm">
                    <input type="hidden" id="departmentId" name="department_id">

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم القسم *</label>
                            <input type="text" class="form-control" id="name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="collegeId" class="form-label">الكلية *</label>
                            <select class="form-select" id="collegeId" name="college_id" required>
                                <option value="">اختر الكلية</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="headName" class="form-label">رئيس القسم</label>
                            <input type="text" class="form-control" id="headName" name="head_name">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="secretaryName" class="form-label">مقرر القسم</label>
                            <input type="text" class="form-control" id="secretaryName" name="secretary_name">
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">وصف القسم</label>
                        <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-warning" onclick="saveDepartment()">
                    <i class="fas fa-save"></i>
                    حفظ
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let departments = [];
let colleges = [];

document.addEventListener('DOMContentLoaded', function() {
    loadColleges();
    loadDepartments();

    // البحث المباشر
    document.getElementById('searchInput').addEventListener('input', function() {
        filterDepartments();
    });

    // تصفية حسب الكلية
    document.getElementById('collegeFilter').addEventListener('change', function() {
        filterDepartments();
    });

    // الترتيب
    document.getElementById('sortBy').addEventListener('change', function() {
        sortDepartments();
    });
});

function loadColleges() {
    fetch('/api/colleges/list')
        .then(response => response.json())
        .then(data => {
            if (Array.isArray(data)) {
                colleges = data;
                updateCollegeSelects();
            } else {
                console.error('خطأ في تحميل الكليات:', data.error);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل الكليات:', error);
        });
}

function updateCollegeSelects() {
    const selects = ['collegeFilter', 'collegeId'];

    selects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            // الاحتفاظ بالخيار الأول
            const firstOption = select.querySelector('option:first-child');
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            // إضافة الكليات
            colleges.forEach(college => {
                const option = document.createElement('option');
                option.value = college.college_id;
                option.textContent = college.name_ar;
                select.appendChild(option);
            });
        }
    });
}

function loadDepartments() {
    fetch('/api/departments')
        .then(response => response.json())
        .then(data => {
            if (Array.isArray(data)) {
                departments = data;
                displayDepartments();
            } else {
                console.error('خطأ في تحميل البيانات:', data.error);
                showAlert('خطأ في تحميل البيانات', 'error');
            }
        })
        .catch(error => {
            console.error('خطأ في الاتصال:', error);
            showAlert('خطأ في الاتصال بالخادم', 'error');
        });
}

function displayDepartments() {
    const tbody = document.getElementById('departmentsTableBody');
    const noDataMessage = document.getElementById('noDataMessage');

    if (departments.length === 0) {
        tbody.innerHTML = '';
        if (noDataMessage) noDataMessage.style.display = 'block';
        return;
    }

    if (noDataMessage) noDataMessage.style.display = 'none';

    tbody.innerHTML = departments.map(department => `
        <tr>
            <td><strong>${department.name}</strong></td>
            <td><span class="college-badge">${department.college_name || 'غير محدد'}</span></td>
            <td>${department.head_name || '-'}</td>
            <td>${department.secretary_name || '-'}</td>
            <td>
                <button class="action-btn btn-edit" onclick="editDepartment(${department.department_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn btn-delete" onclick="deleteDepartment(${department.department_id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function filterDepartments() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const collegeFilter = document.getElementById('collegeFilter').value;

    let filteredDepartments = departments;

    if (searchTerm) {
        filteredDepartments = filteredDepartments.filter(dept =>
            dept.name.toLowerCase().includes(searchTerm) ||
            (dept.head_name && dept.head_name.toLowerCase().includes(searchTerm)) ||
            (dept.secretary_name && dept.secretary_name.toLowerCase().includes(searchTerm))
        );
    }

    if (collegeFilter) {
        filteredDepartments = filteredDepartments.filter(dept =>
            dept.college_id == collegeFilter
        );
    }

    // عرض النتائج المفلترة
    const tbody = document.getElementById('departmentsTableBody');
    tbody.innerHTML = filteredDepartments.map(department => `
        <tr>
            <td><strong>${department.name}</strong></td>
            <td><span class="college-badge">${department.college_name || 'غير محدد'}</span></td>
            <td>${department.head_name || '-'}</td>
            <td>${department.secretary_name || '-'}</td>
            <td>
                <button class="action-btn btn-edit" onclick="editDepartment(${department.department_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="action-btn btn-delete" onclick="deleteDepartment(${department.department_id})" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        </tr>
    `).join('');
}

function sortDepartments() {
    const sortBy = document.getElementById('sortBy').value;
    if (sortBy) {
        departments.sort((a, b) => {
            const aVal = a[sortBy] || '';
            const bVal = b[sortBy] || '';
            return aVal.localeCompare(bVal, 'ar');
        });
        displayDepartments();
    }
}

function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('collegeFilter').value = '';
    document.getElementById('sortBy').value = '';
    displayDepartments();
}

// فتح نافذة الإضافة
function openAddModal() {
    console.log('فتح نافذة إضافة قسم جديد');
    // إعادة تعيين النموذج
    document.getElementById('departmentForm').reset();
    document.getElementById('departmentId').value = '';
    document.getElementById('modalTitle').textContent = 'إضافة قسم جديد';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addDepartmentModal')).show();
}

function editDepartment(id) {
    console.log('تعديل القسم ID:', id);
    const department = departments.find(d => d.department_id === id);
    console.log('بيانات القسم:', department);

    if (department) {
        // تغيير عنوان النافذة
        document.getElementById('modalTitle').textContent = 'تعديل بيانات القسم';

        // ملء الحقول
        document.getElementById('departmentId').value = department.department_id;
        document.getElementById('name').value = department.name;
        document.getElementById('collegeId').value = department.college_id;
        document.getElementById('headName').value = department.head_name || '';
        document.getElementById('secretaryName').value = department.secretary_name || '';
        document.getElementById('description').value = department.description || '';

        console.log('تم ملء الحقول، فتح النافذة...');
        new bootstrap.Modal(document.getElementById('addDepartmentModal')).show();
    } else {
        console.error('لم يتم العثور على القسم');
        showAlert('خطأ: لم يتم العثور على بيانات القسم', 'error');
    }
}

function deleteDepartment(id) {
    if (confirm('هل أنت متأكد من حذف هذا القسم؟\nسيتم حذف جميع البيانات المرتبطة به.')) {
        fetch(`/api/departments/${id}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(result => {
            if (result.error) {
                showAlert(result.error, 'error');
            } else {
                showAlert(result.message, 'success');
                loadDepartments();
            }
        })
        .catch(error => {
            console.error('خطأ:', error);
            showAlert('خطأ في حذف البيانات', 'error');
        });
    }
}

function saveDepartment() {
    const form = document.getElementById('departmentForm');
    const formData = new FormData(form);

    // التحقق من صحة البيانات
    if (!formData.get('name').trim()) {
        showAlert('يرجى إدخال اسم القسم', 'error');
        return;
    }

    if (!formData.get('college_id')) {
        showAlert('يرجى اختيار الكلية', 'error');
        return;
    }

    const departmentId = formData.get('department_id');
    const data = {
        name: formData.get('name'),
        college_id: parseInt(formData.get('college_id')),
        head_name: formData.get('head_name'),
        secretary_name: formData.get('secretary_name'),
        description: formData.get('description')
    };

    const url = departmentId ? `/api/departments/${departmentId}` : '/api/departments';
    const method = departmentId ? 'PUT' : 'POST';

    console.log('حفظ القسم:', {
        departmentId: departmentId,
        method: method,
        url: url,
        data: data
    });

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            showAlert(result.error, 'error');
        } else {
            showAlert(result.message, 'success');
            // إغلاق النافذة وإعادة تحميل البيانات
            bootstrap.Modal.getInstance(document.getElementById('addDepartmentModal')).hide();
            form.reset();
            loadDepartments();
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ البيانات', 'error');
    });
}

function exportData() {
    showAlert('جاري تصدير البيانات...', 'info');
}

function printData() {
    window.print();
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 3000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addDepartmentModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            console.log('إعادة تعيين النموذج...');
            const form = document.getElementById('departmentForm');
            form.reset();
            document.getElementById('departmentId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة قسم جديد';
        });
    }
});
</script>
{% endblock %}

