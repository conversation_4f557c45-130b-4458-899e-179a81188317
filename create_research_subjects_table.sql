-- إن<PERSON>اء جدول مواد السنة البحثية
-- تاريخ الإنشاء: 2025-01-15

USE PGSMS;
GO

-- إن<PERSON>اء جدول مواد السنة البحثية
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='research_subjects' AND xtype='U')
BEGIN
    CREATE TABLE research_subjects (
        id INT IDENTITY(1,1) PRIMARY KEY,
        academic_year_id INT NOT NULL,
        academic_program_id INT NOT NULL,
        subject_name NVARCHAR(100) NOT NULL,
        credit_hours INT NOT NULL DEFAULT 0,
        student_name NVARCHAR(200) NOT NULL,
        grade DECIMAL(5,2) NULL,
        letter_grade NVARCHAR(20) NULL,
        created_date DATETIME DEFAULT GETDATE(),
        updated_date DATETIME DEFAULT GETDATE(),
        
        -- المفاتيح الخارجية
        FOREIGN KEY (academic_year_id) REFERENCES academic_years(year_id),
        FOREIG<PERSON> KEY (academic_program_id) REFERENCES academic_programs(program_id)
    );
    
    PRINT N'تم إنشاء جدول مواد السنة البحثية بنجاح';
END
ELSE
BEGIN
    PRINT N'جدول مواد السنة البحثية موجود بالفعل';
END

-- إنشاء فهارس لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_research_subjects_academic_year')
    CREATE INDEX IX_research_subjects_academic_year ON research_subjects(academic_year_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_research_subjects_program')
    CREATE INDEX IX_research_subjects_program ON research_subjects(academic_program_id);

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_research_subjects_student')
    CREATE INDEX IX_research_subjects_student ON research_subjects(student_name);

-- إنشاء trigger لتحديث تاريخ التعديل
IF NOT EXISTS (SELECT * FROM sys.triggers WHERE name = 'tr_research_subjects_update')
BEGIN
    EXEC('
    CREATE TRIGGER tr_research_subjects_update
    ON research_subjects
    AFTER UPDATE
    AS
    BEGIN
        UPDATE research_subjects 
        SET updated_date = GETDATE()
        WHERE id IN (SELECT id FROM inserted);
    END
    ');
    
    PRINT N'تم إنشاء trigger تحديث التاريخ لجدول مواد السنة البحثية';
END

-- إدراج بيانات تجريبية (اختياري)
/*
INSERT INTO research_subjects (academic_year_id, academic_program_id, subject_name, credit_hours, student_name, grade, letter_grade)
VALUES 
(1, 1, N'السمنار', 2, N'أحمد محمد علي', 85.5, N'جيد جداً'),
(1, 1, N'الرسالة', 6, N'فاطمة حسن عبد الله', 92.0, N'ممتاز'),
(1, 2, N'الاطروحة', 12, N'محمد عبد الرحمن الكريم', 88.0, N'جيد جداً'),
(1, 2, N'مشروع بحث', 4, N'زينب أحمد محمود', 90.5, N'ممتاز');
*/

PRINT N'تم تنفيذ سكريبت إنشاء جدول مواد السنة البحثية بنجاح';
GO
