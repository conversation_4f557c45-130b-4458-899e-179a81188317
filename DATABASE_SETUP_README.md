# نظام إعداد قاعدة البيانات - نظام إدارة الدراسات العليا

## نظرة عامة

تم إضافة نظام شامل لإدارة إعدادات قاعدة البيانات يتضمن:

- **صفحة إعداد قاعدة البيانات**: تظهر عند فشل الاتصال بقاعدة البيانات
- **صفحة الإعدادات**: لإدارة إعدادات النظام وقاعدة البيانات
- **إدارة ديناميكية للاتصال**: حفظ وتحميل إعدادات الاتصال من ملف JSON
- **اختبار الاتصال**: فحص الاتصال بالخادم وقاعدة البيانات
- **إنشاء قاعدة البيانات**: إنشاء قاعدة البيانات والجداول تلقائياً

## الملفات الجديدة

### 1. `database_config.py`
ملف إدارة إعدادات قاعدة البيانات:
- `DatabaseConfig`: كلاس لإدارة إعدادات الاتصال
- `get_db_connection()`: دالة للحصول على اتصال بقاعدة البيانات
- `test_connection()`: دالة لاختبار الاتصال

### 2. `database_config.json`
ملف إعدادات قاعدة البيانات بصيغة JSON:
```json
{
  "server": "HUSSEIN\\SQLEXPRESS",
  "database": "PGSMS",
  "driver": "{ODBC Driver 17 for SQL Server}",
  "authentication": "windows",
  "username": "",
  "password": ""
}
```

### 3. `templates/database_setup.html`
صفحة إعداد قاعدة البيانات التي تظهر عند فشل الاتصال:
- تصميم مستقل وجميل
- نموذج لإدخال معلومات الاتصال
- اختبار الاتصال المباشر
- إنشاء قاعدة البيانات
- خطوات الإعداد الموضحة

### 4. `templates/settings.html`
صفحة الإعدادات الرئيسية:
- إدارة إعدادات قاعدة البيانات
- معلومات النظام
- تحسين قاعدة البيانات
- تصدير الإعدادات
- إجراءات سريعة

### 5. `test_database_setup.py`
ملف اختبار شامل للنظام:
- اختبار الاتصال بقاعدة البيانات
- اختبار حفظ الإعدادات
- اختبار إنشاء قاعدة البيانات
- اختبار تحسين قاعدة البيانات

## المسارات الجديدة (Routes)

### صفحات الويب
- `/database-setup`: صفحة إعداد قاعدة البيانات
- `/settings`: صفحة الإعدادات

### API للإعدادات
- `GET/POST /api/settings/test-connection`: اختبار الاتصال
- `POST /api/settings/save`: حفظ الإعدادات
- `POST /api/settings/create-database`: إنشاء قاعدة البيانات
- `GET /api/settings/system-info`: معلومات النظام
- `POST /api/settings/optimize-database`: تحسين قاعدة البيانات
- `GET /api/settings/export`: تصدير الإعدادات

### API لإعداد قاعدة البيانات
- `POST /api/database-setup/test-connection`: اختبار الاتصال
- `POST /api/database-setup/create-database`: إنشاء قاعدة البيانات
- `POST /api/database-setup/save`: حفظ الإعدادات

## آلية العمل

### 1. فحص الاتصال عند تسجيل الدخول
```python
@app.route('/login', methods=['GET', 'POST'])
def login():
    # فحص الاتصال بقاعدة البيانات أولاً
    if not test_connection():
        return redirect(url_for('database_setup'))
    # ... باقي كود تسجيل الدخول
```

### 2. إدارة الإعدادات
```python
# تحميل الإعدادات
db_config = DatabaseConfig()
config = db_config.load_config()

# حفظ إعدادات جديدة
success = db_config.save_config(new_config)

# اختبار الاتصال
success, message = db_config.test_connection()
```

### 3. إنشاء قاعدة البيانات
```python
# إنشاء قاعدة البيانات والجداول
success, message = db_config.create_database()
```

## المميزات

### 1. **إدارة ديناميكية للإعدادات**
- حفظ الإعدادات في ملف JSON
- تحديث الإعدادات دون إعادة تشغيل الخادم
- دعم أنواع مختلفة من المصادقة

### 2. **واجهة مستخدم متقدمة**
- تصميم responsive يعمل على جميع الأجهزة
- رسائل حالة واضحة
- تأثيرات بصرية جميلة
- دعم اللغة العربية

### 3. **أمان محسن**
- عدم حفظ كلمات المرور في ملفات التصدير
- تشفير آمن للاتصالات
- فحص صلاحيات المستخدم

### 4. **سهولة الاستخدام**
- خطوات واضحة للإعداد
- رسائل خطأ مفصلة
- إرشادات للمستخدم
- اختبار مباشر للاتصال

## كيفية الاستخدام

### 1. **عند فشل الاتصال**
1. سيتم توجيهك تلقائياً إلى صفحة إعداد قاعدة البيانات
2. أدخل معلومات الخادم وقاعدة البيانات
3. اختبر الاتصال
4. أنشئ قاعدة البيانات إذا لم تكن موجودة
5. احفظ الإعدادات

### 2. **من صفحة الإعدادات**
1. انتقل إلى الإعدادات من القائمة الجانبية
2. عدّل إعدادات قاعدة البيانات
3. اختبر الاتصال
4. احفظ التغييرات
5. استخدم الإجراءات السريعة حسب الحاجة

### 3. **اختبار النظام**
```bash
# تشغيل اختبار شامل
python test_database_setup.py
```

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **خطأ: "server was not found"**
   - تحقق من اسم الخادم
   - تأكد من تشغيل SQL Server
   - جرب `localhost` بدلاً من اسم الخادم

2. **خطأ: "Login failed"**
   - تحقق من نوع المصادقة
   - تأكد من صحة اسم المستخدم وكلمة المرور
   - جرب Windows Authentication

3. **خطأ: "database does not exist"**
   - استخدم زر "إنشاء قاعدة البيانات"
   - تأكد من صلاحيات إنشاء قواعد البيانات

4. **خطأ: "ODBC Driver not found"**
   - تأكد من تثبيت ODBC Driver 17 for SQL Server
   - جرب إصدار أقدم من Driver

## التطوير المستقبلي

### مميزات مخطط إضافتها:
- [ ] دعم قواعد بيانات أخرى (MySQL, PostgreSQL)
- [ ] نسخ احتياطي تلقائي للإعدادات
- [ ] مراقبة أداء قاعدة البيانات
- [ ] إشعارات عند فشل الاتصال
- [ ] سجل تفصيلي لعمليات الإعداد

## الأمان

### اعتبارات الأمان:
- كلمات المرور لا تُحفظ في ملفات التصدير
- الإعدادات محمية من الوصول غير المصرح
- فحص صلاحيات المستخدم قبل تنفيذ العمليات
- تشفير الاتصالات مع قاعدة البيانات

---

**ملاحظة**: هذا النظام مصمم للعمل مع SQL Server. للاستخدام مع قواعد بيانات أخرى، يجب تعديل ملف `database_config.py` وإضافة الدعم المناسب.
