{% extends "base.html" %}

{% block title %}إدارة أعضاء هيئة التدريس - نظام إدارة الدراسات العليا{% endblock %}

{% block styles %}
<style>
    .faculty-card {
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
        transition: all 0.3s ease;
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
        border: 1px solid #e9ecef;
    }
    
    .faculty-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-2px);
    }
    
    .faculty-header {
        background: linear-gradient(135deg, #6f42c1 0%, #e83e8c 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 12px 12px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .faculty-body {
        padding: 20px;
    }
    
    .title-badge {
        padding: 4px 12px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .title-professor { background-color: #dc3545; color: white; }
    .title-associate { background-color: #fd7e14; color: white; }
    .title-assistant { background-color: #ffc107; color: #212529; }
    .title-lecturer { background-color: #198754; color: white; }
    .title-instructor { background-color: #6c757d; color: white; }
    
    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-active { background-color: #d1edff; color: #0c63e4; }
    .status-inactive { background-color: #f8d7da; color: #721c24; }
    .status-sabbatical { background-color: #fff3cd; color: #856404; }
    .status-retired { background-color: #f8d7da; color: #721c24; }
    
    .info-row {
        display: flex;
        margin-bottom: 8px;
        align-items: center;
    }
    
    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 120px;
        margin-left: 10px;
    }
    
    .info-value {
        color: #212529;
        flex: 1;
    }

    /* تحسين النماذج */
    .form-text {
        font-size: 0.875rem;
        color: #6c757d;
    }

    .form-control:focus,
    .form-select:focus {
        border-color: #86b7fe;
        box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
    }

    .modal-lg {
        max-width: 900px;
    }

    .info-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .info-row:last-child {
        border-bottom: none;
    }

    .info-label {
        font-weight: 600;
        color: #495057;
        min-width: 120px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-chalkboard-teacher"></i>
        إدارة أعضاء هيئة التدريس
    </h1>
    <p class="page-subtitle">إدارة بيانات أعضاء هيئة التدريس - عرض وإضافة وتعديل وحذف</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#addFacultyModal">
        <i class="fas fa-user-plus"></i>
        إضافة عضو هيئة تدريس جديد
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-secondary" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
        تحديث
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-search"></i>
            البحث والفلترة
        </h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-3">
                <label class="form-label">البحث في الأسماء</label>
                <input type="text" class="form-control" id="searchInput" placeholder="ابحث عن عضو هيئة تدريس..." onkeyup="filterFaculty()">
            </div>
            <div class="col-md-2">
                <label class="form-label">اللقب العلمي</label>
                <select class="form-select" id="titleFilter" onchange="filterFaculty()">
                    <option value="">جميع الألقاب</option>
                    <option value="أستاذ">أستاذ</option>
                    <option value="أستاذ مشارك">أستاذ مشارك</option>
                    <option value="أستاذ مساعد">أستاذ مساعد</option>
                    <option value="محاضر">محاضر</option>
                    <option value="مدرس">مدرس</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">الحالة</label>
                <select class="form-select" id="statusFilter" onchange="filterFaculty()">
                    <option value="">جميع الحالات</option>
                    <option value="نشط">نشط</option>
                    <option value="غير نشط">غير نشط</option>
                    <option value="إجازة علمية">إجازة علمية</option>
                    <option value="متقاعد">متقاعد</option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">القسم</label>
                <select class="form-select" id="departmentFilter" onchange="filterFaculty()">
                    <option value="">جميع الأقسام</option>
                    <!-- سيتم تحميل الأقسام ديناميكياً -->
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">البحث في البريد</label>
                <input type="text" class="form-control" id="emailSearchInput" placeholder="البريد الإلكتروني..." onkeyup="filterFaculty()">
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <button class="btn btn-outline-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4" id="statisticsCards">
    <!-- سيتم تحميل الإحصائيات هنا -->
</div>

<!-- Faculty Container -->
<div id="facultyContainer">
    <div class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">جاري التحميل...</span>
        </div>
        <p class="mt-3 text-muted">جاري تحميل بيانات أعضاء هيئة التدريس...</p>
    </div>
</div>

<!-- Add Faculty Modal -->
<div class="modal fade" id="addFacultyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus"></i>
                    إضافة عضو هيئة تدريس جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addFacultyForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">اسم التدريسي *</label>
                            <input type="text" class="form-control" id="name" name="name" required maxlength="255" placeholder="أدخل اسم التدريسي الكامل">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="degree" class="form-label">الشهادة</label>
                            <select class="form-select" id="degree" name="degree">
                                <option value="">اختر الشهادة</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="دبلوم عالي">دبلوم عالي</option>
                                <option value="دبلوم">دبلوم</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="academic_title" class="form-label">اللقب العلمي</label>
                            <select class="form-select" id="academic_title" name="academic_title">
                                <option value="">اختر اللقب العلمي</option>
                                <option value="أستاذ">أستاذ</option>
                                <option value="أستاذ مشارك">أستاذ مشارك</option>
                                <option value="أستاذ مساعد">أستاذ مساعد</option>
                                <option value="أستاذ متمرس">أستاذ متمرس</option>
                                <option value="محاضر">محاضر</option>
                                <option value="مدرس">مدرس</option>
                                <option value="مدرس مساعد">مدرس مساعد</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="title_date" class="form-label">تاريخ الحصول على اللقب العلمي</label>
                            <input type="date" class="form-control" id="title_date" name="title_date">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="general_specialization" class="form-label">التخصص العام</label>
                            <input type="text" class="form-control" id="general_specialization" name="general_specialization" maxlength="255" placeholder="مثل: علوم الحاسوب">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="specific_specialization" class="form-label">التخصص الدقيق</label>
                            <select class="form-select" id="specific_specialization" name="specific_specialization">
                                <option value="">اختر التخصص الدقيق</option>
                                <!-- سيتم تحميل التخصصات ديناميكياً -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="department_id" class="form-label">القسم</label>
                            <select class="form-select" id="department_id" name="department_id">
                                <option value="">اختر القسم</option>
                                <!-- سيتم تحميل الأقسام ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="mobile_phone" class="form-label">رقم الهاتف النقال</label>
                            <input type="tel" class="form-control" id="mobile_phone" name="mobile_phone" maxlength="20" placeholder="07xxxxxxxxx">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="email" name="email" maxlength="255" placeholder="<EMAIL>">
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- مساحة فارغة للتوازن -->
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="notes" class="form-label">الملاحظات</label>
                            <textarea class="form-control" id="notes" name="notes" rows="3" maxlength="1000" placeholder="أي ملاحظات إضافية حول عضو هيئة التدريس..."></textarea>
                        </div>
                    </div>


                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="addFaculty()">
                    <i class="fas fa-save"></i>
                    حفظ عضو هيئة التدريس
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Faculty Modal -->
<div class="modal fade" id="editFacultyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit"></i>
                    تعديل بيانات عضو هيئة التدريس
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editFacultyForm">
                    <input type="hidden" id="edit_faculty_id" name="faculty_id">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_name" class="form-label">اسم التدريسي *</label>
                            <input type="text" class="form-control" id="edit_name" name="name" required maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_degree" class="form-label">الشهادة</label>
                            <select class="form-select" id="edit_degree" name="degree">
                                <option value="">اختر الشهادة</option>
                                <option value="دكتوراه">دكتوراه</option>
                                <option value="ماجستير">ماجستير</option>
                                <option value="بكالوريوس">بكالوريوس</option>
                                <option value="دبلوم عالي">دبلوم عالي</option>
                                <option value="دبلوم">دبلوم</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_academic_title" class="form-label">اللقب العلمي</label>
                            <select class="form-select" id="edit_academic_title" name="academic_title">
                                <option value="">اختر اللقب العلمي</option>
                                <option value="أستاذ">أستاذ</option>
                                <option value="أستاذ مشارك">أستاذ مشارك</option>
                                <option value="أستاذ مساعد">أستاذ مساعد</option>
                                <option value="أستاذ متمرس">أستاذ متمرس</option>
                                <option value="محاضر">محاضر</option>
                                <option value="مدرس">مدرس</option>
                                <option value="مدرس مساعد">مدرس مساعد</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_title_date" class="form-label">تاريخ الحصول على اللقب العلمي</label>
                            <input type="date" class="form-control" id="edit_title_date" name="title_date">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_general_specialization" class="form-label">التخصص العام</label>
                            <input type="text" class="form-control" id="edit_general_specialization" name="general_specialization" maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_specific_specialization" class="form-label">التخصص الدقيق</label>
                            <select class="form-select" id="edit_specific_specialization" name="specific_specialization">
                                <option value="">اختر التخصص الدقيق</option>
                                <!-- سيتم تحميل التخصصات ديناميكياً -->
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_department_id" class="form-label">القسم</label>
                            <select class="form-select" id="edit_department_id" name="department_id">
                                <option value="">اختر القسم</option>
                                <!-- سيتم تحميل الأقسام ديناميكياً -->
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit_mobile_phone" class="form-label">رقم الهاتف النقال</label>
                            <input type="tel" class="form-control" id="edit_mobile_phone" name="mobile_phone" maxlength="20">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit_email" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="edit_email" name="email" maxlength="255">
                        </div>
                        <div class="col-md-6 mb-3">
                            <!-- مساحة فارغة للتوازن -->
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="edit_notes" class="form-label">الملاحظات</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="3" maxlength="1000"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-primary" onclick="updateFaculty()">
                    <i class="fas fa-save"></i>
                    حفظ التغييرات
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد الحذف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها!
                </div>
                <p>هل أنت متأكد من حذف بيانات عضو هيئة التدريس؟</p>
                <div id="deleteFacultyInfo" class="bg-light p-3 rounded">
                    <!-- سيتم عرض معلومات عضو هيئة التدريس هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">
                    <i class="fas fa-trash"></i>
                    تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
let faculty = [];
let departments = [];
let specializations = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', async function() {
    console.log('🚀 بدء تحميل صفحة أعضاء هيئة التدريس...');

    try {
        // تحميل البيانات المرجعية أولاً
        await loadDepartments();

        // ثم تحميل أعضاء هيئة التدريس
        await loadFaculty();

        console.log('✅ تم تحميل جميع البيانات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error);

        // تحميل احتياطي بعد ثانية واحدة
        setTimeout(async function() {
            console.log('🔄 إعادة محاولة تحميل البيانات...');
            try {
                await loadDepartments();
                await loadFaculty();
            } catch (retryError) {
                console.error('❌ فشل في إعادة المحاولة:', retryError);
            }
        }, 1000);
    }
});

// تحميل قائمة أعضاء هيئة التدريس
async function loadFaculty() {
    try {
        console.log('🔄 تحميل أعضاء هيئة التدريس...');

        // عرض رسالة التحميل
        document.getElementById('facultyContainer').innerHTML = `
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3 text-muted">جاري تحميل بيانات أعضاء هيئة التدريس...</p>
            </div>
        `;

        const response = await fetch('/api/faculty');
        console.log('📡 استجابة أعضاء هيئة التدريس:', response.status);

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        console.log('📊 أعضاء هيئة التدريس المستلمة:', data);

        if (Array.isArray(data) && data.length > 0) {
            faculty = data;
            console.log('✅ تم تحميل', faculty.length, 'عضو هيئة تدريس');
            displayFaculty(faculty);
            updateStatistics();
        } else if (Array.isArray(data) && data.length === 0) {
            console.log('⚠️ لا توجد أعضاء هيئة تدريس في قاعدة البيانات');
            showNoFaculty();
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showError('خطأ في تحميل بيانات أعضاء هيئة التدريس: ' + (data.error || 'بيانات غير صحيحة'));
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل أعضاء هيئة التدريس:', error);
        showError('حدث خطأ أثناء تحميل بيانات أعضاء هيئة التدريس: ' + error.message);
    }
}

// تحميل الأقسام والتخصصات
async function loadDepartments() {
    try {
        // تحميل الأقسام
        const response = await fetch('/api/departments');
        if (response.ok) {
            departments = await response.json();
            console.log('✅ تم تحميل الأقسام:', departments.length, 'قسم');
            console.log('📋 عينة من الأقسام:', departments.slice(0, 2));
            populateDropdown('department_id', departments, 'department_id', 'name');
            populateDropdown('edit_department_id', departments, 'department_id', 'name');
            populateDropdown('departmentFilter', departments, 'department_id', 'name');

            // إعادة عرض أعضاء هيئة التدريس إذا كانوا محملين بالفعل
            if (faculty && faculty.length > 0) {
                console.log('🔄 إعادة عرض أعضاء هيئة التدريس مع أسماء الأقسام...');
                displayFaculty(faculty);
            }
        }

        // تحميل التخصصات
        const specializationsResponse = await fetch('/api/specializations');
        if (specializationsResponse.ok) {
            specializations = await specializationsResponse.json();
            console.log('✅ تم تحميل التخصصات:', specializations.length, 'تخصص');
            console.log('📋 عينة من التخصصات:', specializations.slice(0, 2));
            populateDropdown('specific_specialization', specializations, 'name', 'name');
            populateDropdown('edit_specific_specialization', specializations, 'name', 'name');
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل الأقسام:', error);
    }
}

// ملء القائمة المنسدلة
function populateDropdown(selectId, data, valueField, textField) {
    const select = document.getElementById(selectId);
    if (!select || !Array.isArray(data)) return;

    // الاحتفاظ بالخيار الأول
    const firstOption = select.querySelector('option');
    select.innerHTML = '';
    if (firstOption) {
        select.appendChild(firstOption);
    }

    data.forEach(item => {
        const option = document.createElement('option');
        option.value = item[valueField];
        option.textContent = item[textField];
        option.style.color = '#212529';
        option.style.fontWeight = '500';
        select.appendChild(option);
    });

    // تحسين مظهر القائمة
    select.style.color = '#212529';
    select.style.fontWeight = '500';

    console.log(`✅ تم ملء ${selectId} بـ ${data.length} عنصر`);
}

// فلترة أعضاء هيئة التدريس
function filterFaculty() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const titleFilter = document.getElementById('titleFilter').value;
    const departmentFilter = document.getElementById('departmentFilter').value;
    const emailSearch = document.getElementById('emailSearchInput').value.toLowerCase();

    const filteredFaculty = faculty.filter(member => {
        const matchesSearch = member.name.toLowerCase().includes(searchTerm);
        const matchesTitle = !titleFilter || member.academic_title === titleFilter;
        const matchesDepartment = !departmentFilter || member.department_id == departmentFilter;
        const matchesEmail = !emailSearch || (member.email && member.email.toLowerCase().includes(emailSearch));

        return matchesSearch && matchesTitle && matchesDepartment && matchesEmail;
    });

    displayFaculty(filteredFaculty);
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('titleFilter').value = '';
    document.getElementById('departmentFilter').value = '';
    document.getElementById('emailSearchInput').value = '';

    displayFaculty(faculty);
}

// إضافة عضو هيئة تدريس جديد
function addFaculty() {
    const form = document.getElementById('addFacultyForm');
    const formData = new FormData(form);

    const data = {};
    formData.forEach((value, key) => {
        data[key] = value;
    });

    console.log('📤 إضافة عضو هيئة تدريس جديد:', data);

    fetch('/api/faculty', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('📡 استجابة إضافة عضو هيئة التدريس:', response.status);
        return response.json();
    })
    .then(result => {
        console.log('📊 نتيجة إضافة عضو هيئة التدريس:', result);
        if (result.error) {
            showAlert('خطأ في إضافة عضو هيئة التدريس: ' + result.error, 'error');
        } else {
            showAlert('تم إضافة عضو هيئة التدريس بنجاح', 'success');
            form.reset();
            bootstrap.Modal.getInstance(document.getElementById('addFacultyModal')).hide();
            loadFaculty();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في إضافة عضو هيئة التدريس:', error);
        showAlert('حدث خطأ أثناء إضافة عضو هيئة التدريس: ' + error.message, 'error');
    });
}

// تعديل عضو هيئة تدريس
function editFaculty(facultyId) {
    const member = faculty.find(f => f.faculty_id === facultyId);
    if (!member) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('edit_faculty_id').value = member.faculty_id;
    document.getElementById('edit_name').value = member.name;
    document.getElementById('edit_degree').value = member.degree || '';
    document.getElementById('edit_academic_title').value = member.academic_title || '';
    document.getElementById('edit_title_date').value = member.title_date || '';
    document.getElementById('edit_general_specialization').value = member.general_specialization || '';
    document.getElementById('edit_specific_specialization').value = member.specific_specialization || '';
    document.getElementById('edit_department_id').value = member.department_id || '';
    document.getElementById('edit_mobile_phone').value = member.mobile_phone || '';
    document.getElementById('edit_email').value = member.email || '';
    document.getElementById('edit_notes').value = member.notes || '';

    new bootstrap.Modal(document.getElementById('editFacultyModal')).show();
}

// تحديث بيانات عضو هيئة التدريس
function updateFaculty() {
    const form = document.getElementById('editFacultyForm');
    const formData = new FormData(form);
    const facultyId = formData.get('faculty_id');

    const data = {};
    formData.forEach((value, key) => {
        if (key !== 'faculty_id') {
            data[key] = value;
        }
    });

    console.log('📤 تحديث عضو هيئة التدريس:', facultyId, data);

    fetch(`/api/faculty/${facultyId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(result => {
        if (result.error) {
            showAlert('خطأ في تحديث عضو هيئة التدريس: ' + result.error, 'error');
        } else {
            showAlert('تم تحديث بيانات عضو هيئة التدريس بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('editFacultyModal')).hide();
            loadFaculty();
        }
    })
    .catch(error => {
        console.error('❌ خطأ في تحديث عضو هيئة التدريس:', error);
        showAlert('حدث خطأ أثناء تحديث عضو هيئة التدريس: ' + error.message, 'error');
    });
}

// تأكيد حذف عضو هيئة التدريس
function deleteFaculty(facultyId) {
    const member = faculty.find(f => f.faculty_id === facultyId);
    if (!member) return;

    const titleClass = getTitleClass(member.academic_title);

    document.getElementById('deleteFacultyInfo').innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-chalkboard-teacher me-2"></i>
            <div>
                <strong>${member.name}</strong><br>
                <small class="text-muted">رقم العضو: ${member.faculty_id}</small><br>
                <span class="title-badge ${titleClass}">${member.academic_title || 'غير محدد'}</span>
            </div>
        </div>
    `;

    // تخزين ID عضو هيئة التدريس للحذف
    document.getElementById('confirmDeleteBtn').setAttribute('data-faculty-id', facultyId);

    new bootstrap.Modal(document.getElementById('deleteConfirmModal')).show();
}

// حذف عضو هيئة التدريس
document.addEventListener('DOMContentLoaded', function() {
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    if (confirmBtn) {
        confirmBtn.addEventListener('click', function() {
            const facultyId = this.getAttribute('data-faculty-id');
            if (!facultyId) return;

            fetch(`/api/faculty/${facultyId}`, {
                method: 'DELETE'
            })
            .then(response => response.json())
            .then(result => {
                if (result.error) {
                    showAlert(result.error, 'error');
                } else {
                    showAlert('تم حذف عضو هيئة التدريس بنجاح', 'success');
                    bootstrap.Modal.getInstance(document.getElementById('deleteConfirmModal')).hide();
                    loadFaculty();
                }
            })
            .catch(error => {
                console.error('خطأ:', error);
                showAlert('حدث خطأ أثناء حذف عضو هيئة التدريس', 'error');
            });
        });
    }
});

function refreshData() {
    loadFaculty();
}

function exportData() {
    // تصدير البيانات إلى Excel
    const csvContent = "data:text/csv;charset=utf-8,"
        + "معرف التدريسي,اسم التدريسي,الشهادة,اللقب العلمي,تاريخ الحصول على اللقب,التخصص العام,التخصص الدقيق,القسم,الهاتف النقال,البريد الإلكتروني,الملاحظات,تاريخ الإنشاء\n"
        + faculty.map(member => {
            const department = departments.find(d => d.department_id === member.department_id);
            const titleDate = member.title_date ? new Date(member.title_date).toLocaleDateString('en-GB') : '';

            return `${member.faculty_id},"${member.name}","${member.degree || ''}","${member.academic_title || ''}","${titleDate}","${member.general_specialization || ''}","${member.specific_specialization || ''}","${department ? department.name : ''}","${member.mobile_phone || ''}","${member.email || ''}","${member.notes || ''}","${member.created_date || ''}"`;
        }).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "faculty_data.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// دالة عرض التنبيهات
function showAlert(message, type) {
    // إنشاء تنبيه Bootstrap
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
    alertDiv.style.top = '20px';
    alertDiv.style.right = '20px';
    alertDiv.style.zIndex = '9999';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    // إزالة التنبيه تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.parentNode.removeChild(alertDiv);
        }
    }, 5000);
}

// عرض أعضاء هيئة التدريس
function displayFaculty(facultyToDisplay) {
    console.log('🎨 عرض أعضاء هيئة التدريس:', facultyToDisplay.length, 'عضو');
    const container = document.getElementById('facultyContainer');

    if (!container) {
        console.error('❌ عنصر facultyContainer غير موجود!');
        return;
    }

    if (!facultyToDisplay || facultyToDisplay.length === 0) {
        console.log('⚠️ لا توجد أعضاء هيئة تدريس للعرض');
        showNoFaculty();
        return;
    }

    // التحقق من تحميل البيانات المرجعية
    if (!departments || departments.length === 0) {
        console.log('⚠️ لم يتم تحميل الأقسام بعد، سيتم إعادة المحاولة...');
        setTimeout(() => displayFaculty(facultyToDisplay), 500);
        return;
    }

    // تجميع أعضاء هيئة التدريس حسب القسم
    const facultyByDepartment = {};

    facultyToDisplay.forEach(member => {
        const department = departments.find(d => d.department_id === member.department_id);
        const departmentName = department ? department.name : 'غير محدد القسم';
        const departmentId = department ? department.department_id : 'unknown';

        if (!facultyByDepartment[departmentId]) {
            facultyByDepartment[departmentId] = {
                name: departmentName,
                members: []
            };
        }

        facultyByDepartment[departmentId].members.push(member);
    });

    let html = '';

    // ترتيب الأقسام أبجدياً
    const sortedDepartments = Object.keys(facultyByDepartment).sort((a, b) => {
        return facultyByDepartment[a].name.localeCompare(facultyByDepartment[b].name, 'ar');
    });

    sortedDepartments.forEach((departmentId, index) => {
        const departmentData = facultyByDepartment[departmentId];
        const memberCount = departmentData.members.length;
        const collapseId = `department-${departmentId}`;

        html += `
            <div class="card mb-3">
                <div class="card-header" id="heading-${departmentId}">
                    <h5 class="mb-0">
                        <button class="btn btn-link text-decoration-none w-100 text-start d-flex justify-content-between align-items-center"
                                type="button" data-bs-toggle="collapse" data-bs-target="#${collapseId}"
                                aria-expanded="${index === 0 ? 'true' : 'false'}" aria-controls="${collapseId}">
                            <span>
                                <i class="fas fa-building me-2"></i>
                                ${departmentData.name}
                                <span class="badge bg-primary ms-2">${memberCount}</span>
                            </span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </h5>
                </div>

                <div id="${collapseId}" class="collapse ${index === 0 ? 'show' : ''}"
                     aria-labelledby="heading-${departmentId}">
                    <div class="card-body">
                        <div class="row">
        `;

        // عرض أعضاء هيئة التدريس في هذا القسم
        departmentData.members.forEach(member => {
            const titleClass = getTitleClass(member.academic_title);
            const createdDate = member.created_date ? new Date(member.created_date).toLocaleDateString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }) : 'غير محدد';

            html += `
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="faculty-card">
                        <div class="faculty-header">
                            <div>
                                <h6 class="mb-1">${member.name}</h6>
                                <small>معرف الأستاذ: ${member.faculty_id}</small>
                            </div>
                            <div class="text-left">
                                <span class="title-badge ${titleClass}">${member.academic_title || 'غير محدد'}</span>
                            </div>
                        </div>
                        <div class="faculty-body">
                            ${member.degree ? `
                            <div class="info-row">
                                <span class="info-label">الشهادة:</span>
                                <span class="info-value">${member.degree}</span>
                            </div>
                            ` : ''}
                            ${member.title_date ? `
                            <div class="info-row">
                                <span class="info-label">تاريخ الحصول على اللقب:</span>
                                <span class="info-value">${new Date(member.title_date).toLocaleDateString('en-GB')}</span>
                            </div>
                            ` : ''}
                            ${member.general_specialization ? `
                            <div class="info-row">
                                <span class="info-label">التخصص العام:</span>
                                <span class="info-value">${member.general_specialization}</span>
                            </div>
                            ` : ''}
                            ${member.specific_specialization ? `
                            <div class="info-row">
                                <span class="info-label">التخصص الدقيق:</span>
                                <span class="info-value">${member.specific_specialization}</span>
                            </div>
                            ` : ''}
                            ${member.mobile_phone ? `
                            <div class="info-row">
                                <span class="info-label">الهاتف النقال:</span>
                                <span class="info-value">${member.mobile_phone}</span>
                            </div>
                            ` : ''}
                            ${member.email ? `
                            <div class="info-row">
                                <span class="info-label">البريد الإلكتروني:</span>
                                <span class="info-value">${member.email}</span>
                            </div>
                            ` : ''}
                            ${member.notes ? `
                            <div class="info-row">
                                <span class="info-label">الملاحظات:</span>
                                <span class="info-value">${member.notes}</span>
                            </div>
                            ` : ''}
                            <div class="info-row">
                                <span class="info-label">تاريخ الإضافة:</span>
                                <span class="info-value">${createdDate}</span>
                            </div>

                            <div class="d-flex gap-2 mt-3">
                                <button class="btn btn-sm btn-outline-primary" onclick="editFaculty(${member.faculty_id})">
                                    <i class="fas fa-edit"></i>
                                    تعديل
                                </button>
                                <button class="btn btn-sm btn-outline-danger" onclick="deleteFaculty(${member.faculty_id})">
                                    <i class="fas fa-trash"></i>
                                    حذف
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });

        html += `
                        </div>
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('📊 تحديث الإحصائيات...');

    if (!faculty || faculty.length === 0) {
        console.log('⚠️ لا توجد بيانات أعضاء هيئة التدريس للإحصائيات');
        document.getElementById('statisticsCards').innerHTML = '';
        return;
    }

    const totalFaculty = faculty.length;
    const professors = faculty.filter(f => f.academic_title === 'أستاذ').length;
    const associates = faculty.filter(f => f.academic_title === 'أستاذ مشارك').length;
    const assistants = faculty.filter(f => f.academic_title === 'أستاذ مساعد').length;
    const activeFaculty = faculty.filter(f => f.status === 'نشط').length;

    console.log(`📊 إحصائيات: إجمالي=${totalFaculty}, أساتذة=${professors}, نشط=${activeFaculty}`);

    const html = `
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${totalFaculty}</h4>
                            <p class="mb-0">إجمالي هيئة التدريس</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chalkboard-teacher fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${professors}</h4>
                            <p class="mb-0">أساتذة</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-tie fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${associates}</h4>
                            <p class="mb-0">أساتذة مشاركون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-graduate fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>${assistants}</h4>
                            <p class="mb-0">أساتذة مساعدون</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check-circle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.getElementById('statisticsCards').innerHTML = html;
}

// دوال المساعدة
function getTitleClass(title) {
    switch(title) {
        case 'أستاذ': return 'title-professor';
        case 'أستاذ مشارك': return 'title-associate';
        case 'أستاذ مساعد': return 'title-assistant';
        case 'أستاذ متمرس': return 'title-professor';
        case 'محاضر': return 'title-lecturer';
        case 'مدرس': return 'title-instructor';
        default: return 'title-instructor';
    }
}

// عرض رسالة عدم وجود أعضاء هيئة تدريس
function showNoFaculty() {
    document.getElementById('facultyContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-muted mb-4">
                <i class="fas fa-chalkboard-teacher fa-3x"></i>
            </div>
            <h4 class="text-muted">لا توجد أعضاء هيئة تدريس</h4>
            <p class="text-muted">لم يتم تسجيل أي أعضاء هيئة تدريس في النظام بعد</p>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addFacultyModal">
                <i class="fas fa-user-plus"></i>
                إضافة أول عضو هيئة تدريس
            </button>
        </div>
    `;
}

// عرض رسالة خطأ
function showError(message) {
    document.getElementById('facultyContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
            </div>
            <h4 class="text-danger">خطأ في تحميل البيانات</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadFaculty()">
                <i class="fas fa-sync-alt"></i>
                إعادة المحاولة
            </button>
        </div>
    `;
}
</script>
{% endblock %}
