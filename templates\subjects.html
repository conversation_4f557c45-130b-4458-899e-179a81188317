{% extends "base.html" %}

{% block title %}إدارة المواد الدراسية - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-book"></i>
        إدارة المواد الدراسية
    </h1>
    <p class="page-subtitle">إضافة وتعديل وحذف بيانات المواد الدراسية</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSubjectModal">
        <i class="fas fa-plus"></i>
        إضافة مادة دراسية جديدة
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير البيانات
    </button>
    <button class="btn btn-info" onclick="printData()">
        <i class="fas fa-print"></i>
        طباعة
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <input type="text" class="form-control" id="searchInput" placeholder="البحث في المواد الدراسية...">
            </div>
            <div class="col-md-3">
                <select class="form-select" id="typeFilter">
                    <option value="">جميع الأنواع</option>
                    <option value="إجباري">إجباري</option>
                    <option value="اختياري">اختياري</option>
                    <option value="متطلب جامعة">متطلب جامعة</option>
                </select>
            </div>
            <div class="col-md-3">
                <button class="btn btn-secondary w-100" onclick="clearSearch()">
                    <i class="fas fa-times"></i>
                    مسح البحث
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Subjects Table -->
<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover" id="subjectsTable">
                <thead>
                    <tr>
                        <th>#</th>
                        <th>اسم المادة</th>
                        <th>البرنامج</th>
                        <th>الوحدات</th>
                        <th>الساعات</th>
                        <th>نوع المادة</th>
                        <th>العام الدراسي</th>
                        <th>تاريخ الإضافة</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="subjectsTableBody">
                    <!-- سيتم تحميل البيانات هنا -->
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add Subject Modal -->
<div class="modal fade" id="addSubjectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">إضافة مادة دراسية جديدة</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="subjectForm">
                    <input type="hidden" id="subjectId" name="subject_id">
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="name" class="form-label">اسم المادة *</label>
                            <input type="text" class="form-control" id="name" name="name" required maxlength="255" 
                                   placeholder="أدخل اسم المادة الدراسية">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="programId" class="form-label">البرنامج الأكاديمي *</label>
                        <select class="form-select" id="programId" name="program_id" required>
                            <option value="">اختر البرنامج الأكاديمي</option>
                        </select>
                        <div class="form-text">اختر البرنامج الأكاديمي للمادة</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="units" class="form-label">عدد الوحدات *</label>
                            <input type="number" class="form-control" id="units" name="units" required min="1" max="10" 
                                   placeholder="عدد الوحدات">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="hours" class="form-label">عدد الساعات *</label>
                            <input type="number" class="form-control" id="hours" name="hours" required min="1" max="20" 
                                   placeholder="عدد الساعات">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="subjectType" class="form-label">نوع المادة *</label>
                            <select class="form-select" id="subjectType" name="subject_type" required>
                                <option value="">اختر نوع المادة</option>
                                <option value="إجباري">إجباري</option>
                                <option value="اختياري">اختياري</option>
                                <option value="متطلب جامعة">متطلب جامعة</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="academicYear" class="form-label">العام الدراسي *</label>
                            <select class="form-select" id="academicYear" name="academic_year" required>
                                <option value="">اختر العام الدراسي</option>
                            </select>
                            <div class="form-text">اختر العام الدراسي للمادة</div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-primary" onclick="saveSubject()">حفظ</button>
            </div>
        </div>
    </div>
</div>

<script>
let subjects = [];
let academicPrograms = [];
let academicYears = [];

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    loadSubjects();
    loadAcademicPrograms();
    loadAcademicYears();
});

// تحميل قائمة البرامج الأكاديمية
function loadAcademicPrograms() {
    fetch('/api/academic_programs')
        .then(response => response.json())
        .then(data => {
            academicPrograms = data;
            const programSelect = document.getElementById('programId');
            programSelect.innerHTML = '<option value="">اختر البرنامج الأكاديمي</option>';
            
            data.forEach(program => {
                const option = document.createElement('option');
                option.value = program.program_id;
                option.textContent = `${program.name} (${program.department_name || 'غير محدد'})`;
                programSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل البرامج الأكاديمية:', error);
        });
}

// تحميل قائمة الأعوام الدراسية
function loadAcademicYears() {
    fetch('/api/academic_years')
        .then(response => response.json())
        .then(data => {
            academicYears = data;
            const yearSelect = document.getElementById('academicYear');
            yearSelect.innerHTML = '<option value="">اختر العام الدراسي</option>';

            data.forEach(year => {
                const option = document.createElement('option');
                option.value = year.name;
                option.textContent = year.name;
                yearSelect.appendChild(option);
            });
        })
        .catch(error => {
            console.error('خطأ في تحميل الأعوام الدراسية:', error);
        });
}

// تحميل قائمة المواد الدراسية
function loadSubjects() {
    fetch('/api/subjects')
        .then(response => response.json())
        .then(data => {
            subjects = data;
            displaySubjects(data);
        })
        .catch(error => {
            console.error('خطأ في تحميل البيانات:', error);
            showAlert('خطأ في تحميل البيانات', 'error');
        });
}

// عرض المواد الدراسية في الجدول
function displaySubjects(data) {
    const tbody = document.getElementById('subjectsTableBody');
    tbody.innerHTML = '';

    if (data.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="9" class="text-center py-4">
                    <i class="fas fa-book fa-3x text-muted mb-3"></i>
                    <p class="text-muted">لا توجد مواد دراسية مسجلة حالياً</p>
                </td>
            </tr>
        `;
        return;
    }

    data.forEach((subject, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${index + 1}</td>
            <td>${subject.name}</td>
            <td>${subject.program_name || '-'}</td>
            <td>${subject.units}</td>
            <td>${subject.hours}</td>
            <td><span class="badge bg-primary">${subject.subject_type}</span></td>
            <td>${subject.academic_year}</td>
            <td>${subject.created_date ? new Date(subject.created_date).toLocaleDateString('en-GB') : '-'}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary me-1" onclick="editSubject(${subject.subject_id})" title="تعديل">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteSubject(${subject.subject_id}, '${subject.name}')" title="حذف">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// إضافة أو تحديث مادة دراسية
function saveSubject() {
    const form = document.getElementById('subjectForm');
    const subjectId = document.getElementById('subjectId').value;

    // التحقق من صحة البيانات
    const name = form.querySelector('#name').value.trim();
    const units = form.querySelector('#units').value;
    const hours = form.querySelector('#hours').value;
    const subjectType = form.querySelector('#subjectType').value;
    const academicYear = form.querySelector('#academicYear').value.trim();
    const programId = form.querySelector('#programId').value;

    if (!name || !units || !hours || !subjectType || !academicYear || !programId) {
        showAlert('جميع الحقول المطلوبة يجب ملؤها', 'error');
        return;
    }

    const data = {
        name: name,
        units: parseInt(units),
        hours: parseInt(hours),
        subject_type: subjectType,
        academic_year: academicYear,
        program_id: parseInt(programId)
    };

    const url = subjectId ? `/api/subjects/${subjectId}` : '/api/subjects';
    const method = subjectId ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addSubjectModal')).hide();
            form.reset();
            document.getElementById('subjectId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة مادة دراسية جديدة';
            loadSubjects();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حفظ المادة الدراسية', 'error');
    });
}

// تعديل مادة دراسية
function editSubject(subjectId) {
    const subject = subjects.find(s => s.subject_id === subjectId);
    if (!subject) return;

    // ملء النموذج بالبيانات الحالية
    document.getElementById('subjectId').value = subject.subject_id;
    document.getElementById('name').value = subject.name;
    document.getElementById('units').value = subject.units;
    document.getElementById('hours').value = subject.hours;
    document.getElementById('subjectType').value = subject.subject_type;
    document.getElementById('academicYear').value = subject.academic_year;
    document.getElementById('programId').value = subject.program_id || '';
    document.getElementById('modalTitle').textContent = 'تعديل بيانات المادة الدراسية';

    // فتح النافذة
    new bootstrap.Modal(document.getElementById('addSubjectModal')).show();
}

// حذف مادة دراسية
function deleteSubject(subjectId, subjectName) {
    if (!confirm(`هل أنت متأكد من حذف المادة الدراسية "${subjectName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        return;
    }

    fetch(`/api/subjects/${subjectId}`, {
        method: 'DELETE'
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert(data.message, 'success');
            loadSubjects();
        } else {
            showAlert(data.error || 'حدث خطأ غير متوقع', 'error');
        }
    })
    .catch(error => {
        console.error('خطأ:', error);
        showAlert('خطأ في حذف المادة الدراسية', 'error');
    });
}

// البحث والفلترة
document.getElementById('searchInput').addEventListener('input', function() {
    filterSubjects();
});

document.getElementById('typeFilter').addEventListener('change', function() {
    filterSubjects();
});

function filterSubjects() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const typeFilter = document.getElementById('typeFilter').value;

    const filteredData = subjects.filter(subject => {
        const matchesSearch = subject.name.toLowerCase().includes(searchTerm) ||
                            subject.academic_year.toLowerCase().includes(searchTerm) ||
                            (subject.program_name && subject.program_name.toLowerCase().includes(searchTerm));
        const matchesType = !typeFilter || subject.subject_type === typeFilter;
        
        return matchesSearch && matchesType;
    });

    displaySubjects(filteredData);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    document.getElementById('typeFilter').value = '';
    displaySubjects(subjects);
}

function exportData() {
    showAlert('سيتم تطوير وظيفة التصدير قريباً', 'info');
}

function printData() {
    window.print();
}

// عرض الرسائل
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);

    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}

// إعادة تعيين النموذج عند إغلاق النافذة
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('addSubjectModal');
    if (modal) {
        modal.addEventListener('hidden.bs.modal', function() {
            document.getElementById('subjectForm').reset();
            document.getElementById('subjectId').value = '';
            document.getElementById('modalTitle').textContent = 'إضافة مادة دراسية جديدة';
        });
    }
});
</script>
{% endblock %}
