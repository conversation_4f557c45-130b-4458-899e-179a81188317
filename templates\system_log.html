{% extends "base.html" %}

{% block title %}سجل النظام - نظام إدارة الدراسات العليا{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">
        <i class="fas fa-clipboard-list"></i>
        سجل النظام
    </h1>
    <p class="page-subtitle">عرض وإدارة سجل عمليات النظام</p>
</div>

<!-- Action Buttons -->
<div class="d-flex gap-2 mb-4">
    <button class="btn btn-primary" onclick="refreshData()">
        <i class="fas fa-sync-alt"></i>
        تحديث
    </button>
    <button class="btn btn-success" onclick="exportData()">
        <i class="fas fa-download"></i>
        تصدير
    </button>
    <button class="btn btn-danger" onclick="clearLog()">
        <i class="fas fa-trash"></i>
        مسح السجل
    </button>
</div>

<!-- Search and Filter -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="operationTypeFilter" class="form-label">نوع العملية</label>
                <select class="form-select" id="operationTypeFilter" onchange="filterLogs()">
                    <option value="">جميع العمليات</option>
                    <option value="INSERT">إضافة</option>
                    <option value="UPDATE">تحديث</option>
                    <option value="DELETE">حذف</option>
                    <option value="LOGIN">تسجيل دخول</option>
                    <option value="LOGOUT">تسجيل خروج</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="usernameFilter" class="form-label">المستخدم</label>
                <select class="form-select" id="usernameFilter" onchange="filterLogs()">
                    <option value="">جميع المستخدمين</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="dateFromFilter" class="form-label">من تاريخ</label>
                <input type="date" class="form-control" id="dateFromFilter" onchange="filterLogs()">
            </div>
            <div class="col-md-3">
                <label for="dateToFilter" class="form-label">إلى تاريخ</label>
                <input type="date" class="form-control" id="dateToFilter" onchange="filterLogs()">
            </div>
        </div>
        <div class="row mt-3 g-3">
            <div class="col-md-8">
                <label for="searchInput" class="form-label">البحث في العمليات</label>
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="ابحث في اسم العملية أو الكائن المستهدف..." onkeyup="filterLogs()">
                    <button class="btn btn-outline-secondary" type="button" onclick="clearSearch()">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="col-md-4 d-flex align-items-end">
                <button class="btn btn-secondary w-100" onclick="clearFilters()">
                    <i class="fas fa-times"></i>
                    مسح الفلاتر
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card bg-primary text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-list fa-2x mb-2"></i>
                <h3 id="totalLogs">0</h3>
                <p class="mb-0">إجمالي العمليات</p>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card bg-success text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-plus fa-2x mb-2"></i>
                <h3 id="insertOperations">0</h3>
                <p class="mb-0">عمليات الإضافة</p>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card bg-warning text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-edit fa-2x mb-2"></i>
                <h3 id="updateOperations">0</h3>
                <p class="mb-0">عمليات التحديث</p>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-lg-6 col-md-6">
        <div class="card bg-danger text-white h-100">
            <div class="card-body text-center">
                <i class="fas fa-trash fa-2x mb-2"></i>
                <h3 id="deleteOperations">0</h3>
                <p class="mb-0">عمليات الحذف</p>
            </div>
        </div>
    </div>
</div>

<!-- System Log Table -->
<div class="card">
    <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">
                <i class="fas fa-history"></i>
                سجل العمليات
            </h5>
            <div class="d-flex align-items-center">
                <span class="me-3 text-muted">عرض</span>
                <select class="form-select form-select-sm" id="pageSize" onchange="changePageSize()" style="width: auto;">
                    <option value="10">10</option>
                    <option value="25" selected>25</option>
                    <option value="50">50</option>
                    <option value="100">100</option>
                </select>
                <span class="ms-2 text-muted">سجل</span>
            </div>
        </div>

        <div id="logContainer">
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <p class="mt-3 text-muted">جاري تحميل سجل النظام...</p>
            </div>
        </div>

        <!-- Pagination -->
        <div id="paginationContainer" class="d-flex justify-content-between align-items-center mt-3" style="display: none !important;">
            <div class="text-muted">
                عرض <span id="showingFrom">1</span> إلى <span id="showingTo">25</span> من <span id="totalRecords">0</span> سجل
            </div>
            <nav aria-label="صفحات سجل النظام">
                <ul class="pagination pagination-sm mb-0" id="pagination">
                    <!-- سيتم إنشاء أزرار الصفحات هنا -->
                </ul>
            </nav>
        </div>
    </div>
</div>

<!-- Clear Log Confirmation Modal -->
<div class="modal fade" id="clearLogModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title text-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    تأكيد مسح السجل
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-warning"></i>
                    <strong>تحذير:</strong> هذه العملية ستحذف جميع سجلات النظام نهائياً ولا يمكن التراجع عنها!
                </div>
                <p>هل أنت متأكد من رغبتك في مسح جميع سجلات النظام؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i>
                    إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmClearLog()">
                    <i class="fas fa-trash"></i>
                    مسح السجل
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let systemLogs = [];
let filteredLogs = [];
let currentPage = 1;
let pageSize = 25;
let totalPages = 1;

// تحميل البيانات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 بدء تحميل صفحة سجل النظام...');
    loadSystemLogs();
});

// تحميل سجل النظام
async function loadSystemLogs() {
    try {
        console.log('🔄 تحميل سجل النظام...');
        
        const response = await fetch('/api/system_log');
        console.log('📡 استجابة سجل النظام:', response.status);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        console.log('📊 سجل النظام المستلم:', data);
        
        if (Array.isArray(data) && data.length > 0) {
            systemLogs = data;
            console.log('✅ تم تحميل', systemLogs.length, 'سجل');
            displaySystemLogs(systemLogs);
            updateStatistics();
            populateUserFilter();
        } else if (Array.isArray(data) && data.length === 0) {
            console.log('⚠️ لا توجد سجلات في النظام');
            showNoLogs();
        } else {
            console.error('❌ البيانات ليست مصفوفة:', data);
            showError('خطأ في تحميل سجل النظام: ' + (data.error || 'بيانات غير صحيحة'));
        }
    } catch (error) {
        console.error('❌ خطأ في تحميل سجل النظام:', error);
        showError('حدث خطأ أثناء تحميل سجل النظام: ' + error.message);
    }
}

// عرض سجل النظام مع الصفحات
function displaySystemLogs(logsToDisplay) {
    console.log('🎨 عرض سجل النظام:', logsToDisplay.length, 'سجل');
    const container = document.getElementById('logContainer');

    if (!container) {
        console.error('❌ عنصر logContainer غير موجود!');
        return;
    }

    if (!logsToDisplay || logsToDisplay.length === 0) {
        console.log('⚠️ لا توجد سجلات للعرض');
        showNoLogs();
        return;
    }

    // حساب الصفحات
    totalPages = Math.ceil(logsToDisplay.length / pageSize);
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const currentPageLogs = logsToDisplay.slice(startIndex, endIndex);

    let html = `
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>رقم السجل</th>
                        <th>اسم العملية</th>
                        <th>نوع العملية</th>
                        <th>الكائن المستهدف</th>
                        <th>المستخدم</th>
                        <th>تاريخ العملية</th>
                    </tr>
                </thead>
                <tbody>
    `;

    currentPageLogs.forEach(log => {
        const operationDate = log.operation_date ? new Date(log.operation_date).toLocaleString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        }) : 'غير محدد';
        const operationTypeClass = getOperationTypeClass(log.operation_type);

        html += `
            <tr>
                <td><span class="badge bg-secondary">${log.log_id}</span></td>
                <td>${log.operation_name}</td>
                <td><span class="badge ${operationTypeClass}">${log.operation_type}</span></td>
                <td>${log.target_object}</td>
                <td>
                    <i class="fas fa-user"></i>
                    ${log.username}
                </td>
                <td>
                    <i class="fas fa-clock"></i>
                    ${operationDate}
                </td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;

    // تحديث معلومات الصفحات
    updatePaginationInfo(logsToDisplay.length, startIndex + 1, Math.min(endIndex, logsToDisplay.length));

    // إنشاء أزرار الصفحات
    createPagination();
}

// الحصول على فئة CSS لنوع العملية
function getOperationTypeClass(operationType) {
    switch(operationType) {
        case 'INSERT': return 'bg-success';
        case 'UPDATE': return 'bg-warning';
        case 'DELETE': return 'bg-danger';
        case 'LOGIN': return 'bg-info';
        case 'LOGOUT': return 'bg-secondary';
        default: return 'bg-primary';
    }
}

// تحديث الإحصائيات
function updateStatistics() {
    console.log('📊 تحديث الإحصائيات...');

    const totalLogs = systemLogs.length;
    const insertOps = systemLogs.filter(log => log.operation_type === 'INSERT').length;
    const updateOps = systemLogs.filter(log => log.operation_type === 'UPDATE').length;
    const deleteOps = systemLogs.filter(log => log.operation_type === 'DELETE').length;

    document.getElementById('totalLogs').textContent = totalLogs;
    document.getElementById('insertOperations').textContent = insertOps;
    document.getElementById('updateOperations').textContent = updateOps;
    document.getElementById('deleteOperations').textContent = deleteOps;
}

// ملء فلتر المستخدمين
function populateUserFilter() {
    const userFilter = document.getElementById('usernameFilter');
    const users = [...new Set(systemLogs.map(log => log.username))].sort();

    // مسح الخيارات الحالية (عدا الخيار الأول)
    userFilter.innerHTML = '<option value="">جميع المستخدمين</option>';

    users.forEach(username => {
        const option = document.createElement('option');
        option.value = username;
        option.textContent = username;
        userFilter.appendChild(option);
    });
}

// فلترة السجلات
function filterLogs() {
    const operationType = document.getElementById('operationTypeFilter').value;
    const username = document.getElementById('usernameFilter').value;
    const dateFrom = document.getElementById('dateFromFilter').value;
    const dateTo = document.getElementById('dateToFilter').value;
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();

    filteredLogs = systemLogs.filter(log => {
        const matchesType = !operationType || log.operation_type === operationType;
        const matchesUser = !username || log.username === username;
        const matchesSearch = !searchTerm ||
            log.operation_name.toLowerCase().includes(searchTerm) ||
            log.target_object.toLowerCase().includes(searchTerm);

        let matchesDateRange = true;
        if (dateFrom || dateTo) {
            const logDate = new Date(log.operation_date);
            if (dateFrom) {
                const fromDate = new Date(dateFrom);
                matchesDateRange = matchesDateRange && logDate >= fromDate;
            }
            if (dateTo) {
                const toDate = new Date(dateTo);
                toDate.setHours(23, 59, 59, 999); // نهاية اليوم
                matchesDateRange = matchesDateRange && logDate <= toDate;
            }
        }

        return matchesType && matchesUser && matchesSearch && matchesDateRange;
    });

    currentPage = 1; // إعادة تعيين الصفحة عند الفلترة
    displaySystemLogs(filteredLogs);
}

// مسح البحث
function clearSearch() {
    document.getElementById('searchInput').value = '';
    filterLogs();
}

// مسح الفلاتر
function clearFilters() {
    document.getElementById('operationTypeFilter').value = '';
    document.getElementById('usernameFilter').value = '';
    document.getElementById('dateFromFilter').value = '';
    document.getElementById('dateToFilter').value = '';
    document.getElementById('searchInput').value = '';

    displaySystemLogs(systemLogs);
}

// تحديث البيانات
function refreshData() {
    loadSystemLogs();
}

// تصدير البيانات
function exportData() {
    const dataToExport = filteredLogs.length > 0 ? filteredLogs : systemLogs;

    const csvContent = "data:text/csv;charset=utf-8,"
        + "رقم السجل,اسم العملية,نوع العملية,الكائن المستهدف,المستخدم,تاريخ العملية\n"
        + dataToExport.map(log => {
            const operationDate = log.operation_date ? new Date(log.operation_date).toLocaleString('en-GB', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            }) : '';
            return `${log.log_id},"${log.operation_name}","${log.operation_type}","${log.target_object}","${log.username}","${operationDate}"`;
        }).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `system_log_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// مسح السجل
function clearLog() {
    const modal = new bootstrap.Modal(document.getElementById('clearLogModal'));
    modal.show();
}

// تأكيد مسح السجل
async function confirmClearLog() {
    try {
        const response = await fetch('/api/system_log/clear', {
            method: 'DELETE'
        });

        if (response.ok) {
            const result = await response.json();
            showAlert('تم مسح سجل النظام بنجاح', 'success');
            bootstrap.Modal.getInstance(document.getElementById('clearLogModal')).hide();
            loadSystemLogs();
        } else {
            const error = await response.json();
            showAlert('خطأ في مسح السجل: ' + error.error, 'error');
        }
    } catch (error) {
        console.error('❌ خطأ في مسح السجل:', error);
        showAlert('حدث خطأ أثناء مسح السجل: ' + error.message, 'error');
    }
}

// عرض رسالة عدم وجود سجلات
function showNoLogs() {
    document.getElementById('logContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-muted mb-4">
                <i class="fas fa-clipboard-list fa-3x"></i>
            </div>
            <h4 class="text-muted">لا توجد سجلات</h4>
            <p class="text-muted">لم يتم تسجيل أي عمليات في النظام بعد</p>
        </div>
    `;
}

// عرض رسالة خطأ
function showError(message) {
    document.getElementById('logContainer').innerHTML = `
        <div class="text-center py-5">
            <div class="text-danger mb-4">
                <i class="fas fa-exclamation-triangle fa-3x"></i>
            </div>
            <h4 class="text-danger">خطأ في تحميل البيانات</h4>
            <p class="text-muted">${message}</p>
            <button class="btn btn-primary" onclick="loadSystemLogs()">
                <i class="fas fa-sync-alt"></i>
                إعادة المحاولة
            </button>
        </div>
    `;
}

// تحديث معلومات الصفحات
function updatePaginationInfo(total, from, to) {
    document.getElementById('totalRecords').textContent = total;
    document.getElementById('showingFrom').textContent = from;
    document.getElementById('showingTo').textContent = to;

    const paginationContainer = document.getElementById('paginationContainer');
    if (total > 0) {
        paginationContainer.style.display = 'flex';
    } else {
        paginationContainer.style.display = 'none';
    }
}

// إنشاء أزرار الصفحات
function createPagination() {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';

    if (totalPages <= 1) return;

    // زر الصفحة السابقة
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">السابق</a>`;
    pagination.appendChild(prevLi);

    // أزرار الصفحات
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    if (startPage > 1) {
        const firstLi = document.createElement('li');
        firstLi.className = 'page-item';
        firstLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(1)">1</a>`;
        pagination.appendChild(firstLi);

        if (startPage > 2) {
            const dotsLi = document.createElement('li');
            dotsLi.className = 'page-item disabled';
            dotsLi.innerHTML = `<span class="page-link">...</span>`;
            pagination.appendChild(dotsLi);
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }

    if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
            const dotsLi = document.createElement('li');
            dotsLi.className = 'page-item disabled';
            dotsLi.innerHTML = `<span class="page-link">...</span>`;
            pagination.appendChild(dotsLi);
        }

        const lastLi = document.createElement('li');
        lastLi.className = 'page-item';
        lastLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${totalPages})">${totalPages}</a>`;
        pagination.appendChild(lastLi);
    }

    // زر الصفحة التالية
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">التالي</a>`;
    pagination.appendChild(nextLi);
}

// تغيير الصفحة
function changePage(page) {
    if (page < 1 || page > totalPages || page === currentPage) return;

    currentPage = page;
    displaySystemLogs(filteredLogs);
}

// تغيير حجم الصفحة
function changePageSize() {
    pageSize = parseInt(document.getElementById('pageSize').value);
    currentPage = 1;
    displaySystemLogs(filteredLogs);
}

// عرض التنبيهات
function showAlert(message, type) {
    // يمكن تحسين هذه الدالة لاحقاً لعرض تنبيهات أفضل
    if (type === 'success') {
        alert('✅ ' + message);
    } else if (type === 'error') {
        alert('❌ ' + message);
    } else {
        alert(message);
    }
}
</script>
{% endblock %}
